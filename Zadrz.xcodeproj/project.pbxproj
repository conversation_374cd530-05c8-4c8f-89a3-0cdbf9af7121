// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		C1B1691C2E05C68100CEF551 /* FirebaseAuth in Frameworks */ = {isa = PBXBuildFile; productRef = C1B1691B2E05C68100CEF551 /* FirebaseAuth */; };
		C1B1691E2E05C68100CEF551 /* FirebaseCore in Frameworks */ = {isa = PBXBuildFile; productRef = C1B1691D2E05C68100CEF551 /* FirebaseCore */; };
		C1B169202E05C68100CEF551 /* FirebaseDatabase in Frameworks */ = {isa = PBXBuildFile; productRef = C1B1691F2E05C68100CEF551 /* FirebaseDatabase */; };
		C1B169222E05C68100CEF551 /* FirebaseMessaging in Frameworks */ = {isa = PBXBuildFile; productRef = C1B169212E05C68100CEF551 /* FirebaseMessaging */; };
		C1B169242E05C68100CEF551 /* FirebaseStorage in Frameworks */ = {isa = PBXBuildFile; productRef = C1B169232E05C68100CEF551 /* FirebaseStorage */; };
		C1B169612E072AE000CEF551 /* GoogleSignIn in Frameworks */ = {isa = PBXBuildFile; productRef = C1B169602E072AE000CEF551 /* GoogleSignIn */; };
		C1B169632E072AE000CEF551 /* GoogleSignInSwift in Frameworks */ = {isa = PBXBuildFile; productRef = C1B169622E072AE000CEF551 /* GoogleSignInSwift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		C1A9C3D22DF7A4A700239188 /* Zadrz.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Zadrz.app; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		C1B169712E07BF0400CEF551 /* Exceptions for "Zadrz" folder in "Zadrz" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = C1A9C3D12DF7A4A700239188 /* Zadrz */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		C1A9C3D42DF7A4A700239188 /* Zadrz */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				C1B169712E07BF0400CEF551 /* Exceptions for "Zadrz" folder in "Zadrz" target */,
			);
			path = Zadrz;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		C1A9C3CF2DF7A4A700239188 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C1B169222E05C68100CEF551 /* FirebaseMessaging in Frameworks */,
				C1B169202E05C68100CEF551 /* FirebaseDatabase in Frameworks */,
				C1B169632E072AE000CEF551 /* GoogleSignInSwift in Frameworks */,
				C1B1691E2E05C68100CEF551 /* FirebaseCore in Frameworks */,
				C1B169242E05C68100CEF551 /* FirebaseStorage in Frameworks */,
				C1B1691C2E05C68100CEF551 /* FirebaseAuth in Frameworks */,
				C1B169612E072AE000CEF551 /* GoogleSignIn in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		C1A9C3C92DF7A4A700239188 = {
			isa = PBXGroup;
			children = (
				C1A9C3D42DF7A4A700239188 /* Zadrz */,
				C1A9C3D32DF7A4A700239188 /* Products */,
			);
			sourceTree = "<group>";
		};
		C1A9C3D32DF7A4A700239188 /* Products */ = {
			isa = PBXGroup;
			children = (
				C1A9C3D22DF7A4A700239188 /* Zadrz.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		C1A9C3D12DF7A4A700239188 /* Zadrz */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C1A9C3DD2DF7A4A800239188 /* Build configuration list for PBXNativeTarget "Zadrz" */;
			buildPhases = (
				C1A9C3CE2DF7A4A700239188 /* Sources */,
				C1A9C3CF2DF7A4A700239188 /* Frameworks */,
				C1A9C3D02DF7A4A700239188 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				C1A9C3D42DF7A4A700239188 /* Zadrz */,
			);
			name = Zadrz;
			packageProductDependencies = (
				C1B1691B2E05C68100CEF551 /* FirebaseAuth */,
				C1B1691D2E05C68100CEF551 /* FirebaseCore */,
				C1B1691F2E05C68100CEF551 /* FirebaseDatabase */,
				C1B169212E05C68100CEF551 /* FirebaseMessaging */,
				C1B169232E05C68100CEF551 /* FirebaseStorage */,
				C1B169602E072AE000CEF551 /* GoogleSignIn */,
				C1B169622E072AE000CEF551 /* GoogleSignInSwift */,
			);
			productName = Zadrz;
			productReference = C1A9C3D22DF7A4A700239188 /* Zadrz.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		C1A9C3CA2DF7A4A700239188 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					C1A9C3D12DF7A4A700239188 = {
						CreatedOnToolsVersion = 16.4;
					};
				};
			};
			buildConfigurationList = C1A9C3CD2DF7A4A700239188 /* Build configuration list for PBXProject "Zadrz" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = C1A9C3C92DF7A4A700239188;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				C1B1691A2E05C68100CEF551 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */,
				C1B1695F2E072AE000CEF551 /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = C1A9C3D32DF7A4A700239188 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				C1A9C3D12DF7A4A700239188 /* Zadrz */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		C1A9C3D02DF7A4A700239188 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		C1A9C3CE2DF7A4A700239188 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		C1A9C3DB2DF7A4A800239188 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = V992GDVW43;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		C1A9C3DC2DF7A4A800239188 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = V992GDVW43;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		C1A9C3DE2DF7A4A800239188 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = V992GDVW43;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Zadrz/Info.plist;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.alijaver.Zadrz;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		C1A9C3DF2DF7A4A800239188 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = V992GDVW43;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Zadrz/Info.plist;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.alijaver.Zadrz;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		C1A9C3CD2DF7A4A700239188 /* Build configuration list for PBXProject "Zadrz" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C1A9C3DB2DF7A4A800239188 /* Debug */,
				C1A9C3DC2DF7A4A800239188 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C1A9C3DD2DF7A4A800239188 /* Build configuration list for PBXNativeTarget "Zadrz" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C1A9C3DE2DF7A4A800239188 /* Debug */,
				C1A9C3DF2DF7A4A800239188 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		C1B1691A2E05C68100CEF551 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/firebase/firebase-ios-sdk";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 11.14.0;
			};
		};
		C1B1695F2E072AE000CEF551 /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/google/GoogleSignIn-iOS";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 8.0.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		C1B1691B2E05C68100CEF551 /* FirebaseAuth */ = {
			isa = XCSwiftPackageProductDependency;
			package = C1B1691A2E05C68100CEF551 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseAuth;
		};
		C1B1691D2E05C68100CEF551 /* FirebaseCore */ = {
			isa = XCSwiftPackageProductDependency;
			package = C1B1691A2E05C68100CEF551 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseCore;
		};
		C1B1691F2E05C68100CEF551 /* FirebaseDatabase */ = {
			isa = XCSwiftPackageProductDependency;
			package = C1B1691A2E05C68100CEF551 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseDatabase;
		};
		C1B169212E05C68100CEF551 /* FirebaseMessaging */ = {
			isa = XCSwiftPackageProductDependency;
			package = C1B1691A2E05C68100CEF551 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseMessaging;
		};
		C1B169232E05C68100CEF551 /* FirebaseStorage */ = {
			isa = XCSwiftPackageProductDependency;
			package = C1B1691A2E05C68100CEF551 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseStorage;
		};
		C1B169602E072AE000CEF551 /* GoogleSignIn */ = {
			isa = XCSwiftPackageProductDependency;
			package = C1B1695F2E072AE000CEF551 /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */;
			productName = GoogleSignIn;
		};
		C1B169622E072AE000CEF551 /* GoogleSignInSwift */ = {
			isa = XCSwiftPackageProductDependency;
			package = C1B1695F2E072AE000CEF551 /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */;
			productName = GoogleSignInSwift;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = C1A9C3CA2DF7A4A700239188 /* Project object */;
}
