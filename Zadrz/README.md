# Zadrz - Chat with Legends

A SwiftUI application that allows users to chat with AI-powered historical figures and experts.

## Architecture

This project follows a clean architecture pattern with the following structure:

### Core Layer
- **Models**: Data models and entities
- **Services**: Business logic and external API interactions
- **Utilities**: Helper classes, constants, and extensions
- **Navigation**: Centralized navigation and routing

### Features Layer
- **Authentication**: Login, signup, and user management
- **Chat**: Chat interface and messaging functionality
- **Profile**: User profile and settings
- **Onboarding**: User onboarding flow

### Shared Layer
- **Components**: Reusable UI components
- **Extensions**: View and utility extensions

## Key Technologies

- **Swift 6.0+**: Latest Swift features and concurrency
- **SwiftUI**: Declarative UI framework
- **SwiftData**: Data persistence (planned)
- **Combine**: Reactive programming
- **Firebase**: Authentication and backend services
- **Async/Await**: Modern concurrency

## Design Principles

- **SOLID**: Single responsibility, open/closed, Liskov substitution, interface segregation, dependency inversion
- **KISS**: Keep it simple, stupid
- **DRY**: Don't repeat yourself
- **YAGNI**: You aren't gonna need it
- **POP**: Protocol-oriented programming

## Performance Optimizations

- Lazy loading for expensive views
- Debounced search for better performance
- Memory-efficient image caching
- Optimized list rendering
- View update minimization

## Getting Started

1. Clone the repository
2. Open `Zadrz.xcodeproj` in Xcode
3. Add your Firebase configuration file
4. Build and run

## Project Structure

```
Zadrz/
├── App/                    # App entry point and main views
├── Core/                   # Core business logic
│   ├── Models/            # Data models
│   ├── Services/          # Business services
│   ├── Utilities/         # Helper utilities
│   ├── Extensions/        # Extensions
│   └── Navigation/        # Navigation logic
├── Features/              # Feature modules
│   ├── Authentication/    # Auth flow
│   ├── Chat/             # Chat functionality
│   ├── Profile/          # User profile
│   └── Onboarding/       # User onboarding
├── Shared/               # Shared components
│   └── Components/       # Reusable UI components
└── Resources/            # Assets and resources
```

## Contributing

1. Follow the established architecture patterns
2. Use the existing components when possible
3. Write tests for new functionality
4. Follow Swift and SwiftUI best practices
5. Update documentation for significant changes

## License

[Add your license here]
