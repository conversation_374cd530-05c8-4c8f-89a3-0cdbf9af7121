import SwiftUI

struct AppRootView: View {
    @StateObject private var authVM = AuthScreenViewModel()
    @State private var isAuthenticated: Bool? = nil
    
    var body: some View {
        Group {
            if isAuthenticated == nil {
                // Loading splash
                ProgressView("Loading...")
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .task {
                        await authVM.tryAutoLogin()
                        isAuthenticated = authVM.isAuthenticated
                    }
            } else if isAuthenticated == false {
                GreetingView(authVM: authVM) {
                    // On login success callback
                    authVM.isAuthenticated = true
                    isAuthenticated = true
                }
            } else {
                MainTabView(authVM: authVM)
            }
        }
        .onReceive(authVM.$isAuthenticated) { value in
            isAuthenticated = value
        }
    }
}

struct MainTabView: View {
    @ObservedObject var authVM: AuthScreenViewModel
    @State private var selectedTab: Tab = .avatars
    @State private var tabChangeTrigger = false
    
    var body: some View {
        TabView(selection: $selectedTab) {
//            HomeView()
//                .tabItem {
//                    Label(Tab.home.title, systemImage: Tab.home.icon)
//                }
//                .tag(Tab.home)
            
            AvatarSelectionView()
                .tabItem {
                    Label(Tab.avatars.title, systemImage: Tab.avatars.icon)
                }
                .tag(Tab.avatars)
            
            ChatsView()
                .tabItem {
                    Label(Tab.chats.title, systemImage: Tab.chats.icon)
                }
                .tag(Tab.chats)
            
       
            
            ProfileView(authVM: authVM)
                .tabItem {
                    Label(Tab.profile.title, systemImage: Tab.profile.icon)
                }
                .tag(Tab.profile)
        }
        .sensoryFeedback(.selection, trigger: tabChangeTrigger)
        .onChange(of: selectedTab) { _, newValue in
            tabChangeTrigger.toggle()
        }
    }
    
    private enum Tab: String, CaseIterable {
        case chats, avatars, profile
        
        var title: String {
            self.rawValue.capitalized
        }
        
        var icon: String {
            switch self {
            case .chats: return "message"
            case .avatars: return "person.2"
            case .profile: return "person"
            }
        }
    }
}
