//
//  AppRootView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

struct AppRootView: View {
    @StateObject private var authViewModel = AuthenticationViewModel()
    @State private var isAuthenticated: Bool? = nil
    
    var body: some View {
        Group {
            if isAuthenticated == nil {
                LoadingView()
                    .task {
                        await authViewModel.tryAutoLogin()
                        isAuthenticated = authViewModel.isAuthenticated
                    }
            } else if isAuthenticated == false {
                GreetingView(authViewModel: authViewModel) {
                    authViewModel.isAuthenticated = true
                    isAuthenticated = true
                }
            } else {
                MainTabView(authViewModel: authViewModel)
            }
        }
        .onReceive(authViewModel.$isAuthenticated) { value in
            isAuthenticated = value
        }
    }
}

// MARK: - Loading View
private struct LoadingView: View {
    var body: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            
            Text("Loading...")
                .font(.headline)
                .foregroundStyle(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(.ultraThinMaterial)
    }
}

#Preview {
    AppRootView()
}
