//
//  MainTabView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

struct MainTabView: View {
    @ObservedObject var authViewModel: AuthenticationViewModel
    @StateObject private var navigationRouter = NavigationRouter()
    @State private var tabChangeTrigger = false

    var body: some View {
        TabView(selection: $navigationRouter.selectedTab) {
            ForEach(TabItem.allCases, id: \.self) { tab in
                NavigationStack(path: $navigationRouter.path) {
                    tabContent(for: tab)
                        .withNavigationDestinations()
                }
                .tabItem {
                    Label(tab.title, systemImage: tab.icon)
                }
                .tag(tab)
                .environmentObject(navigationRouter)
                .environmentObject(authViewModel)
            }
        }
        .sensoryFeedback(.selection, trigger: tabChangeTrigger)
        .onChange(of: navigationRouter.selectedTab) { _, _ in
            tabChangeTrigger.toggle()
        }
    }

    // MARK: - Tab Content
    @ViewBuilder
    private func tabContent(for tab: TabItem) -> some View {
        switch tab {
        case .chats:
            ChatsView()
        case .avatars:
            AvatarSelectionView()
        case .profile:
            ProfileView(authViewModel: authViewModel)
        }
    }
}

#Preview {
    MainTabView(authViewModel: AuthenticationViewModel())
}
