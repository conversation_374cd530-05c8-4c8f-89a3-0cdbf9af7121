# Swift Testing Migration Guide

This document outlines the migration from XCTest to Swift Testing in the Zadrz project and provides guidelines for writing modern tests.

## Why Swift Testing?

Swift Testing is Apple's modern testing framework that provides:

- **Better syntax**: More readable and expressive test code
- **Async support**: Built-in async/await testing capabilities
- **Parameterized tests**: Test multiple inputs efficiently
- **Performance testing**: Built-in timing and benchmarks
- **Better organization**: Suites and tags for test management
- **Improved debugging**: Better error messages and stack traces

## Migration Summary

### Before (XCTest)
```swift
import XCTest
@testable import Zadrz

final class AuthenticationViewModelTests: XCTestCase {
    var viewModel: AuthenticationViewModel!
    
    override func setUp() {
        super.setUp()
        viewModel = AuthenticationViewModel()
    }
    
    func testSignUpWithValidData() {
        // Given
        viewModel.email = "<EMAIL>"
        
        // When
        await viewModel.signUp()
        
        // Then
        XCTAssertTrue(viewModel.isAuthenticated)
        XCTAssertNotNil(viewModel.successMessage)
    }
}
```

### After (Swift Testing)
```swift
import Testing
@testable import Zadrz

@Suite("Authentication View Model Tests")
struct AuthenticationViewModelTests {
    
    @Test("Sign up with valid data succeeds")
    func signUpWithValidData() async {
        // Given
        let viewModel = AuthenticationViewModel()
        viewModel.email = "<EMAIL>"
        
        // When
        await viewModel.signUp()
        
        // Then
        #expect(viewModel.isAuthenticated)
        #expect(viewModel.successMessage != nil)
    }
}
```

## Key Differences

### 1. Test Declaration
- **XCTest**: `func testMethodName()`
- **Swift Testing**: `@Test("Description") func methodName()`

### 2. Assertions
- **XCTest**: `XCTAssertEqual(a, b)`
- **Swift Testing**: `#expect(a == b)`

### 3. Test Organization
- **XCTest**: Classes with inheritance
- **Swift Testing**: Structs with `@Suite`

### 4. Setup/Teardown
- **XCTest**: `setUp()` and `tearDown()` methods
- **Swift Testing**: Helper functions or initialization

## Advanced Features

### 1. Parameterized Tests

```swift
@Test("Email validation", arguments: [
    ("<EMAIL>", true),
    ("invalid-email", false),
    ("", false)
])
func emailValidation(email: String, expectedValid: Bool) {
    #expect(email.isValidEmail == expectedValid)
}
```

### 2. Performance Testing

```swift
@Test("Large dataset performance", .timeLimit(.seconds(1)))
func largeDatasetPerformance() async {
    let largeArray = Array(0..<10000)
    
    let startTime = Date()
    let _ = largeArray.filter { $0 % 2 == 0 }
    let endTime = Date()
    
    let duration = endTime.timeIntervalSince(startTime)
    #expect(duration < 0.1) // Should complete in less than 100ms
}
```

### 3. Async Testing

```swift
@Test("Async operation completes successfully")
func asyncOperation() async throws {
    let result = try await someAsyncFunction()
    #expect(result.isSuccess)
}
```

### 4. Error Testing

```swift
@Test("Invalid input throws error")
func invalidInputThrowsError() async {
    await #expect(throws: ValidationError.invalidEmail) {
        try await validateEmail("invalid-email")
    }
}
```

### 5. Conditional Tests

```swift
@Test("Performance test", .enabled(if: ProcessInfo.processInfo.environment["ENABLE_PERFORMANCE_TESTS"] == "true"))
func performanceTest() {
    // Only runs when performance testing is enabled
}
```

### 6. Test Tags

```swift
@Suite("UI Tests", .tags(.ui, .integration))
struct UITests {
    
    @Test("Button interaction", .tags(.ui))
    func buttonInteraction() {
        // UI-specific test
    }
}

extension Tag {
    @Tag static var ui: Self
    @Tag static var integration: Self
    @Tag static var performance: Self
}
```

## Best Practices

### 1. Test Organization

```swift
// Group related tests in suites
@Suite("Authentication Tests")
struct AuthenticationTests {
    
    @Suite("Sign Up Flow")
    struct SignUpTests {
        // Sign up specific tests
    }
    
    @Suite("Sign In Flow") 
    struct SignInTests {
        // Sign in specific tests
    }
}
```

### 2. Helper Functions

```swift
@Suite("User Model Tests")
struct UserModelTests {
    
    // Helper function for creating test data
    func createTestUser() -> UserModel {
        return UserModel(
            id: "test",
            fullName: "Test User",
            email: "<EMAIL>"
        )
    }
    
    @Test("User initialization")
    func userInitialization() {
        let user = createTestUser()
        #expect(user.displayName == "Test User")
    }
}
```

### 3. Custom Assertions

```swift
struct TestAssertions {
    static func expectInRange<T: Comparable>(
        _ value: T,
        _ range: ClosedRange<T>
    ) {
        #expect(range.contains(value), "Value \(value) not in range \(range)")
    }
}

@Test("Value is in expected range")
func valueInRange() {
    TestAssertions.expectInRange(5, 1...10)
}
```

### 4. Mock Services

```swift
// Protocol-based mocking
protocol AuthenticationServiceProtocol {
    func login(email: String, password: String) async throws
}

struct MockAuthenticationService: AuthenticationServiceProtocol {
    var shouldSucceed = true
    var loginCalled = false
    
    func login(email: String, password: String) async throws {
        loginCalled = true
        if !shouldSucceed {
            throw AuthenticationError.invalidCredentials
        }
    }
}

@Test("Login with mock service")
func loginWithMockService() async throws {
    let mockService = MockAuthenticationService()
    mockService.shouldSucceed = true
    
    try await mockService.login(email: "<EMAIL>", password: "password")
    
    #expect(mockService.loginCalled)
}
```

## Running Tests

### Command Line
```bash
# Run all tests
xcodebuild test -scheme Zadrz

# Run specific suite
xcodebuild test -scheme Zadrz -only-testing:ZadrzTests.AuthenticationViewModelTests

# Run with environment variables
ENABLE_PERFORMANCE_TESTS=true xcodebuild test -scheme Zadrz
```

### Xcode
1. Use `Cmd+U` to run all tests
2. Use the test navigator to run specific tests
3. Use the diamond icons in the gutter to run individual tests

## Migration Checklist

- [x] Replace `XCTest` imports with `Testing`
- [x] Convert test classes to structs with `@Suite`
- [x] Replace `XCTAssert*` with `#expect`
- [x] Update test method signatures with `@Test`
- [x] Convert setUp/tearDown to helper functions
- [x] Add parameterized tests where appropriate
- [x] Implement performance tests with time limits
- [x] Add test tags for organization
- [x] Create mock services with protocols
- [x] Update documentation and guides

## Benefits Achieved

1. **Cleaner Syntax**: Tests are more readable and maintainable
2. **Better Performance**: Built-in performance testing capabilities
3. **Improved Organization**: Suites and tags for better test management
4. **Modern Async Support**: Native async/await testing
5. **Enhanced Debugging**: Better error messages and stack traces
6. **Parameterized Testing**: Efficient testing of multiple inputs
7. **Conditional Execution**: Environment-based test control

## Resources

- [Swift Testing Documentation](https://developer.apple.com/documentation/testing)
- [WWDC 2024: Meet Swift Testing](https://developer.apple.com/videos/play/wwdc2024/10179/)
- [Swift Testing GitHub](https://github.com/apple/swift-testing)

This migration positions the Zadrz project with modern testing practices and improved developer experience.
