//
//  FlexibleTagLayout.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

struct FlexibleTagLayout<Data: Collection, Content: View>: View where Data.Element: Hashable {
    let data: Data
    let spacing: CGFloat
    let content: (Data.Element) -> Content
    
    var body: some View {
        FlowLayout(spacing: spacing) {
            ForEach(Array(data), id: \.self) { item in
                content(item)
            }
        }
    }
}

struct FlowLayout: Layout {
    let spacing: CGFloat
    
    init(spacing: CGFloat = 8) {
        self.spacing = spacing
    }
    
    func sizeThatFits(proposal: ProposedViewSize, subviews: Subviews, cache: inout ()) -> CGSize {
        let sizes = subviews.map { $0.sizeThatFits(.unspecified) }
        return layout(sizes: sizes, in: proposal.replacingUnspecifiedDimensions()).size
    }
    
    func placeSubviews(in bounds: CGRect, proposal: ProposedViewSize, subviews: Subviews, cache: inout ()) {
        let sizes = subviews.map { $0.sizeThatFits(.unspecified) }
        let offsets = layout(sizes: sizes, in: proposal.replacingUnspecifiedDimensions()).offsets
        
        for (subview, offset) in zip(subviews, offsets) {
            subview.place(at: CGPoint(x: bounds.minX + offset.x, y: bounds.minY + offset.y), proposal: .unspecified)
        }
    }
    
    private func layout(sizes: [CGSize], in containerSize: CGSize) -> (size: CGSize, offsets: [CGPoint]) {
        var result: [CGPoint] = []
        var currentRowX: CGFloat = 0
        var currentRowY: CGFloat = 0
        var currentRowHeight: CGFloat = 0
        var totalHeight: CGFloat = 0
        var currentRowSizes: [CGSize] = []
        var currentRowStartIndex = 0
        
        for (index, size) in sizes.enumerated() {
            if currentRowX + size.width > containerSize.width && !currentRowSizes.isEmpty {
                // Center the current row
                let rowWidth = currentRowSizes.reduce(0) { $0 + $1.width } + CGFloat(currentRowSizes.count - 1) * spacing
                let rowStartX = max(0, (containerSize.width - rowWidth) / 2)
                
                // Update positions for current row
                var x = rowStartX
                for i in currentRowStartIndex..<index {
                    result[i] = CGPoint(x: x, y: currentRowY)
                    x += sizes[i].width + spacing
                }
                
                // Move to next row
                currentRowY += currentRowHeight + spacing
                totalHeight = currentRowY
                currentRowX = 0
                currentRowHeight = 0
                currentRowSizes.removeAll()
                currentRowStartIndex = index
            }
            
            currentRowSizes.append(size)
            currentRowX += size.width + spacing
            currentRowHeight = max(currentRowHeight, size.height)
            result.append(.zero) // Placeholder, will be updated
        }
        
        // Handle last row
        if !currentRowSizes.isEmpty {
            let rowWidth = currentRowSizes.reduce(0) { $0 + $1.width } + CGFloat(currentRowSizes.count - 1) * spacing
            let rowStartX = max(0, (containerSize.width - rowWidth) / 2)
            
            var x = rowStartX
            for i in currentRowStartIndex..<sizes.count {
                result[i] = CGPoint(x: x, y: currentRowY)
                x += sizes[i].width + spacing
            }
            totalHeight = currentRowY + currentRowHeight
        }
        
        return (CGSize(width: containerSize.width, height: totalHeight), result)
    }
}

#Preview {
    FlexibleTagLayout(
        data: ["Swift", "SwiftUI", "iOS Development", "Xcode", "Programming"],
        spacing: 8
    ) { tag in
        Text(tag)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(.regularMaterial, in: .capsule)
    }
    .padding()
}
