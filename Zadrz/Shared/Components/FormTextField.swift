//
//  FormTextField.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

struct FormTextField: View {
    let title: String
    let placeholder: String
    @Binding var text: String
    var isSecure: Bool = false
    var keyboardType: UIKeyboardType = .default
    var autocapitalization: TextInputAutocapitalization = .sentences
    var errorMessage: String? = nil
    
    @State private var isSecureTextVisible = false
    @FocusState private var isFocused: Bool
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.headline)
                .fontWeight(.medium)
                .foregroundStyle(.primary)
            
            HStack {
                Group {
                    if isSecure && !isSecureTextVisible {
                        SecureField(placeholder, text: $text)
                    } else {
                        TextField(placeholder, text: $text)
                            .keyboardType(keyboardType)
                            .textInputAutocapitalization(autocapitalization)
                    }
                }
                .focused($isFocused)
                .textFieldStyle(CustomTextFieldStyle(
                    isFocused: isFocused,
                    hasError: errorMessage != nil
                ))
                
                if isSecure {
                    Button {
                        isSecureTextVisible.toggle()
                    } label: {
                        Image(systemName: isSecureTextVisible ? AppConstants.SystemImages.eyeSlash : AppConstants.SystemImages.eye)
                            .foregroundStyle(.secondary)
                    }
                    .padding(.trailing, 12)
                }
            }
            
            if let errorMessage = errorMessage {
                Text(errorMessage)
                    .font(.caption)
                    .foregroundStyle(.red)
                    .transition(.opacity)
            }
        }
        .animation(.easeInOut(duration: AppConstants.UI.Animation.fastDuration), value: errorMessage)
    }
}

// MARK: - Custom Text Field Style
struct CustomTextFieldStyle: TextFieldStyle {
    let isFocused: Bool
    let hasError: Bool
    
    func _body(configuration: TextField<Self._Label>) -> some View {
        configuration
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(.regularMaterial)
            .clipShape(RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius))
            .overlay(
                RoundedRectangle(cornerRadius: AppConstants.UI.cornerRadius)
                    .stroke(borderColor, lineWidth: borderWidth)
            )
    }
    
    private var borderColor: Color {
        if hasError {
            return .red
        } else if isFocused {
            return .accentColor
        } else {
            return .clear
        }
    }
    
    private var borderWidth: CGFloat {
        hasError || isFocused ? 2 : 0
    }
}

#Preview {
    VStack(spacing: 20) {
        FormTextField(
            title: "Email",
            placeholder: "Enter your email",
            text: .constant(""),
            keyboardType: .emailAddress,
            autocapitalization: .never
        )
        
        FormTextField(
            title: "Password",
            placeholder: "Enter your password",
            text: .constant(""),
            isSecure: true
        )
        
        FormTextField(
            title: "Name",
            placeholder: "Enter your name",
            text: .constant(""),
            errorMessage: "This field is required"
        )
    }
    .padding()
}
