//
//  CapsuleButton.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

struct CapsuleButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.system(size: 14, weight: .medium))
                .foregroundStyle(isSelected ? .black : .accentColor)
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(
                    Capsule()
                        .fill(isSelected ? Color.accentColor : .clear)
                        .overlay(
                            Capsule()
                                .stroke(.accent, lineWidth: 1)
                        )
                )
        }
        .buttonStyle(.plain)
        .sensoryFeedback(.selection, trigger: isSelected)
    }
}

#Preview {
    HStack {
        CapsuleButton(title: "Selected", isSelected: true) {}
        CapsuleButton(title: "Unselected", isSelected: false) {}
    }
    .padding()
}
