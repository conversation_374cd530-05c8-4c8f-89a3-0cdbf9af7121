//
//  PrimaryButton.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

struct PrimaryButton: View {
    let title: String
    let isLoading: Bool
    let isEnabled: Bool
    let action: () -> Void
    
    init(
        title: String,
        isLoading: Bool = false,
        isEnabled: Bool = true,
        action: @escaping () -> Void
    ) {
        self.title = title
        self.isLoading = isLoading
        self.isEnabled = isEnabled
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            HStack {
                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .black))
                        .scaleEffect(0.8)
                } else {
                    Text(title)
                }
            }
            .buttonStyle(isEnabled: isEnabled && !isLoading)
        }
        .disabled(!isEnabled || isLoading)
    }
}

struct SecondaryButton: View {
    let title: String
    let isLoading: Bool
    let isEnabled: Bool
    let action: () -> Void
    
    init(
        title: String,
        isLoading: Bool = false,
        isEnabled: Bool = true,
        action: @escaping () -> Void
    ) {
        self.title = title
        self.isLoading = isLoading
        self.isEnabled = isEnabled
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            HStack {
                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .accentColor))
                        .scaleEffect(0.8)
                } else {
                    Text(title)
                }
            }
            .buttonStyle(
                backgroundColor: .clear,
                foregroundColor: .accentColor,
                isEnabled: isEnabled && !isLoading
            )
            .overlay(
                RoundedRectangle(cornerRadius: AppConstants.UI.buttonHeight / 2)
                    .stroke(Color.accentColor, lineWidth: 2)
            )
        }
        .disabled(!isEnabled || isLoading)
    }
}

#Preview {
    VStack(spacing: 20) {
        PrimaryButton(title: "Primary Button") {
            print("Primary tapped")
        }
        
        PrimaryButton(title: "Loading", isLoading: true) {
            print("Loading tapped")
        }
        
        SecondaryButton(title: "Secondary Button") {
            print("Secondary tapped")
        }
        
        SecondaryButton(title: "Disabled", isEnabled: false) {
            print("Disabled tapped")
        }
    }
    .padding()
}
