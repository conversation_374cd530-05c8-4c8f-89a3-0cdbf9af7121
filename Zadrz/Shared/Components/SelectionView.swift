//
//  SelectionView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

struct SelectionView: View {
    let title: String
    let options: [String]
    let allowMultipleSelection: Bool
    let onNext: ([String]) -> Void
    
    @Environment(\.dismiss) private var dismiss
    @State private var selectedOptions: Set<String> = []
    
    init(
        title: String,
        options: [String],
        allowMultipleSelection: Bool = true,
        onNext: @escaping ([String]) -> Void
    ) {
        self.title = title
        self.options = options
        self.allowMultipleSelection = allowMultipleSelection
        self.onNext = onNext
    }
    
    var body: some View {
        VStack(spacing: 0) {
            optionsSection
            Spacer()
            actionButtons
        }
        .navigationTitle(title)
        .navigationBarTitleDisplayMode(.large)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("Skip") {
                    dismiss()
                }
                .foregroundColor(.accentColor)
            }
        }
    }
    
    // MARK: - Private Views
    private var optionsSection: some View {
        VStack {
            Spacer()
            
            FlexibleTagLayout(
                data: options,
                spacing: 12
            ) { option in
                CapsuleButton(
                    title: option,
                    isSelected: selectedOptions.contains(option)
                ) {
                    toggleOption(option)
                }
            }
            .standardPadding()
            
            Spacer()
        }
    }
    
    private var actionButtons: some View {
        VStack(spacing: 16) {
            PrimaryButton(
                title: "Next",
                isEnabled: !selectedOptions.isEmpty
            ) {
                handleNext()
            }
            .standardPadding()
            
            if !allowMultipleSelection {
                SecondaryButton(
                    title: "None of the above"
                ) {
                    selectedOptions.removeAll()
                    handleNext()
                }
                .standardPadding()
            }
        }
        .padding(.bottom, AppConstants.UI.largePadding)
    }
    
    // MARK: - Private Methods
    private func toggleOption(_ option: String) {
        withAnimation(.easeInOut(duration: AppConstants.UI.Animation.fastDuration)) {
            if allowMultipleSelection {
                if selectedOptions.contains(option) {
                    selectedOptions.remove(option)
                } else {
                    selectedOptions.insert(option)
                }
            } else {
                if selectedOptions.contains(option) {
                    selectedOptions.remove(option)
                } else {
                    selectedOptions.removeAll()
                    selectedOptions.insert(option)
                }
            }
        }
    }
    
    private func handleNext() {
        onNext(Array(selectedOptions))
    }
}

#Preview {
    NavigationStack {
        SelectionView(
            title: "Choose Your Interests",
            options: ["Technology", "Science", "Art", "Music", "Sports", "Travel", "Food", "Books"],
            allowMultipleSelection: true
        ) { selectedOptions in
            print("Selected: \(selectedOptions)")
        }
    }
}
