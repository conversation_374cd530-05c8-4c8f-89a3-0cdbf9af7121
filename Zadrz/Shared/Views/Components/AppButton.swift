//
//  AppButton.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

struct AppButton: View {
    let title: String
    let icon: Image?
    let iconPosition: IconPosition
    let action: () -> Void
    let backgroundColor: Color?
    let foregroundColor: Color
    let isLoading: Bool
    
    enum IconPosition {
        case leading, trailing
    }
    
    init(
        title: String,
        icon: Image? = nil,
        iconPosition: IconPosition = .leading,
        action: @escaping () -> Void,
        backgroundColor: Color? = .accentColor,
        foregroundColor: Color = Color(.label),
        isLoading: Bool = false
    ) {
        self.title = title
        self.icon = icon
        self.iconPosition = iconPosition
        self.action = action
        self.backgroundColor = backgroundColor
        self.foregroundColor = foregroundColor
        self.isLoading = isLoading
    }
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 8) {
                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: foregroundColor))
                        .scaleEffect(0.8)
                } else {
                    if iconPosition == .leading, let icon = icon {
                        icon
                    }
                    
                    Text(title)
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    if iconPosition == .trailing, let icon = icon {
                        icon
                    }
                }
            }
            .frame(maxWidth: .infinity)
            .frame(height: 52)
            .foregroundStyle(foregroundColor)
            .colorInvert()
            .background(backgroundColor)
            .clipShape(.capsule)
        }
        .disabled(isLoading)
    }

}

#Preview {
    VStack(spacing: 16) {
        AppButton(title: "Sign Up", ) {
            // Preview action
            
        }
        
        AppButton(
            title: "Sign in with Apple",
            icon: Image(systemName: "apple.logo"),
            iconPosition: .leading,
            action: {},
            backgroundColor: .black,
            foregroundColor: .white
        )
        
        AppButton(
            title: "Sign in with Google",
            icon: Image(systemName: "g.circle.fill"),
            iconPosition: .leading,
            action: {},
            backgroundColor: .black,
            foregroundColor: .black
        )
    }
    .padding()
}
