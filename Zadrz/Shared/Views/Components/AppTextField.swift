//
//  AppTextField.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//


import SwiftUI

struct AppTextField: View {
    let title: String
    let placeholder: String
    @Binding var text: String
    let isSecure: Bool
    let keyboardType: UIKeyboardType
    let autocapitalization: TextInputAutocapitalization
    let showInfoButton: Bool
    let infoAction: (() -> Void)?
    let autocorrectionDisabled: Bool
    let onSubmit: (() -> Void)?
    
    @State private var isPasswordVisible = false
    @State private var showAlert = false
    @State private var alertMessage = ""
    @FocusState private var isFocused: Bool
    
    init(
        title: String,
        placeholder: String,
        text: Binding<String>,
        isSecure: Bool = false,
        keyboardType: UIKeyboardType = .default,
        autocapitalization: TextInputAutocapitalization = .never,
        showInfoButton: Bool = false,
        infoAction: (() -> Void)? = nil,
        autocorrectionDisabled: Bool = true,
        onSubmit: (() -> Void)? = nil
    ) {
        self.title = title
        self.placeholder = placeholder
        self._text = text
        self.isSecure = isSecure
        self.keyboardType = keyboardType
        self.autocapitalization = autocapitalization
        self.showInfoButton = showInfoButton
        self.infoAction = infoAction
        self.autocorrectionDisabled = autocorrectionDisabled
        self.onSubmit = onSubmit
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.caption)
                .foregroundColor(.accent)
                .padding(.leading, 4)
            
            HStack {
                if isSecure && !isPasswordVisible {
                    SecureField(placeholder, text: $text)
                        .textFieldStyle(.plain)
                        .textContentType(.password)
                        .focused($isFocused)
                        .onChange(of: text) { _, newValue in
                            filterInput(newValue)
                        }
                        .onSubmit {
                            handleSubmit()
                        }
                } else {
                    TextField(placeholder, text: $text)
                        .textFieldStyle(.plain)
                        .keyboardType(keyboardType)
                        .textInputAutocapitalization(autocapitalization)
                        .autocorrectionDisabled(autocorrectionDisabled)
                        .focused($isFocused)
                        .onChange(of: text) { _, newValue in
                            filterInput(newValue)
                        }
                        .onSubmit {
                            handleSubmit()
                        }
                }
                
                HStack(spacing: 8) {
                    // Show/Hide password button for secure fields
                    if isSecure {
                        Button(action: {
                            isPasswordVisible.toggle()
                        }) {
                            Image(systemName: isPasswordVisible ? "eye" : "eye.slash")
                                .foregroundColor(
                                    .secondary)
                        }
                    }
                    
                    // Clear button
                    if !text.isEmpty {
                        Button(action: {
                            text = ""
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    // Info button
                    if showInfoButton {
                        Button(action: {
                            infoAction?()
                        }) {
                            Image(systemName: "info.circle.fill")
                                .foregroundColor(.secondary)
                        }
                    }
                }
            }
            .padding()
            .background(Color.gray.opacity(0.1))
            .clipShape(.rect(cornerRadius: 12))
        }
        .alert("Validation Error", isPresented: $showAlert) {
            Button("OK") { }
        } message: {
            Text(alertMessage)
        }
    }
    
    private func handleSubmit() {
        guard validateField() else { return }
        onSubmit?()
    }
    
    private func filterInput(_ newValue: String) {
        if keyboardType == .phonePad {
            // Phone: only numbers and + at the beginning
            let filtered = newValue.enumerated().compactMap { index, char in
                if char == "+" && index == 0 {
                    return char
                } else if char.isNumber {
                    return char
                }
                return nil
            }
            let filteredString = String(filtered)
            if text != filteredString {
                text = filteredString
            }
        }
    }
    
    @discardableResult
    private func validateField() -> Bool {
        // Check if field is empty
        if text.isEmpty {
            showAlert(message: "\(title) cannot be empty")
            return false
        }
        
        if keyboardType == .emailAddress {
            return validateEmail()
        } else if keyboardType == .phonePad {
            return validatePhone()
        } else if isSecure {
            return validatePassword()
        }
        
        return true
    }
    
    @discardableResult
    private func validateEmail() -> Bool {
        let emailRegex = "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)
        
        if !emailPredicate.evaluate(with: text) {
            showAlert(message: "Please enter a valid email address with @ symbol")
            return false
        }
        return true
    }
    
    @discardableResult
    private func validatePhone() -> Bool {
        let phoneRegex = "^\\+?[1-9]\\d{1,14}$"
        let phonePredicate = NSPredicate(format: "SELF MATCHES %@", phoneRegex)
        
        if !phonePredicate.evaluate(with: text) {
            showAlert(message: "Please enter a valid phone number starting with + and containing only digits")
            return false
        }
        return true
    }
    
    @discardableResult
    private func validatePassword() -> Bool {
        var errors: [String] = []
        
        if text.count < 6 || text.count > 12 {
            errors.append("Password must be between 6-12 characters")
        }
        
        if !text.contains(where: { $0.isUppercase }) {
            errors.append("Password must contain at least one uppercase letter")
        }
        
        if !text.contains(where: { $0.isLowercase }) {
            errors.append("Password must contain at least one lowercase letter")
        }
        
        let specialCharacters = "!@#$%^&*()_+-=[]{}|;:,.<>?"
        if !text.contains(where: { specialCharacters.contains($0) }) {
            errors.append("Password must contain at least one special character (!@#$%^&*()_+-=[]{}|;:,.<>?)")
        }
        
        if !errors.isEmpty {
            showAlert(message: errors.joined(separator: "\n"))
            return false
        }
        return true
    }
    
    private func showAlert(message: String) {
        alertMessage = message
        showAlert = true
    }
}

#Preview {
    ScrollView {
        VStack(spacing: 16) {
            AppTextField(
                title: "Email",
                placeholder: "<EMAIL>",
                text: .constant(""),
                keyboardType: .emailAddress
            )
            
            AppTextField(
                title: "Phone",
                placeholder: "+1234567890",
                text: .constant(""),
                keyboardType: .phonePad
            )
            
            AppTextField(
                title: "Password",
                placeholder: "6-12 characters",
                text: .constant(""),
                isSecure: true
            )
            
            AppTextField(
                title: "Full name",
                placeholder: "Lee Byuong",
                text: .constant(""),
                showInfoButton: true,
                infoAction: {
                    print("Info tapped")
                }
            )
        }
        .padding()
    }
    .scrollDismissesKeyboard(.interactively)
}
