//
//  DataModels.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import Foundation

// MARK: - Chat Item
struct ChatItem: Identifiable, Hashable {
    let id = UUID()
    let name: String
    let message: String
    let time: String
    let unreadCount: Int
    let isRead: Bool
    
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    static func == (lhs: ChatItem, rhs: ChatItem) -> Bool {
        lhs.id == rhs.id
    }
    
    static let sampleChats = [
        ChatItem(name: "<PERSON><PERSON>", message: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do", time: "22:30", unreadCount: 10, isRead: false),
        ChatItem(name: "<PERSON><PERSON>", message: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do", time: "22:30", unreadCount: 10, isRead: false),
        Chat<PERSON><PERSON>(name: "<PERSON><PERSON>", message: "<PERSON>rem ipsum dolor sit amet, consectetur adipiscing elit, sed do", time: "22:30", unreadCount: 10, isRead: false),
        ChatI<PERSON>(name: "<PERSON>na Garifyan", message: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do", time: "22:30", unreadCount: 0, isRead: true),
        ChatItem(name: "Alina Garifyan", message: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do", time: "22:30", unreadCount: 0, isRead: true),
        ChatItem(name: "Alina Garifyan", message: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do", time: "22:30", unreadCount: 0, isRead: true),
        ChatItem(name: "Alina Garifyan", message: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do", time: "22:30", unreadCount: 0, isRead: true)
    ]
}

// MARK: - Chat Message
struct ChatMessage: Identifiable {
    let id = UUID()
    let text: String
    let isFromUser: Bool
    let timestamp: String
    let messageType: MessageType
    
    init(text: String, isFromUser: Bool, timestamp: String, messageType: MessageType = .text) {
        self.text = text
        self.isFromUser = isFromUser
        self.timestamp = timestamp
        self.messageType = messageType
    }
    
    static let sampleMessages: [ChatMessage] = [
        ChatMessage(text: "dolor sit", isFromUser: true, timestamp: "22:30"),
        ChatMessage(text: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt", isFromUser: false, timestamp: "22:30"),
        ChatMessage(text: "Lorem ipsum dolor sit amet", isFromUser: true, timestamp: "22:30"),
        ChatMessage(text: "document_name.doc", isFromUser: false, timestamp: "22:30", messageType: .file),
        ChatMessage(text: "July 5", isFromUser: false, timestamp: "", messageType: .date),
        ChatMessage(text: "Dolore magna aliqua", isFromUser: false, timestamp: "22:30", messageType: .image),
        ChatMessage(text: "See older messages...", isFromUser: false, timestamp: "22:30")
    ]
}

enum MessageType {
    case text, file, image, date
}