//
//  ZadrzApp.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI
import FirebaseCore

@main
struct ZadrzApp: App {

    init() {
        configureApp()
    }

    var body: some Scene {
        WindowGroup {
            AppRootView()
        }
    }

    // MARK: - Private Methods
    private func configureApp() {
        PerformanceMonitor.shared.startMeasuring()

        // Configure Firebase
        FirebaseApp.configure()

        // Log app launch performance
        PerformanceMonitor.shared.endMeasuring(operation: "App Launch Configuration")
        PerformanceMonitor.shared.measureMemoryUsage()

        #if DEBUG
        print("🚀 Zadrz app launched successfully")
        print("📱 Running on \(UIDevice.current.model)")
        print("📊 iOS \(UIDevice.current.systemVersion)")
        #endif
    }
}
