import SwiftUI

struct SelectionView: View {
    let title: String
    let options: [String]
    let allowMultipleSelection: Bool
    let onNext: ([String]) -> Void
    
    @Environment(\.dismiss) private var dismiss
    @State private var selectedOptions: Set<String> = []
    @State private var shouldNavigateToNext = false
    
    init(
        title: String,
        options: [String],
        allowMultipleSelection: Bool = true,
        onNext: @escaping ([String]) -> Void
    ) {
        self.title = title
        self.options = options
        self.allowMultipleSelection = allowMultipleSelection
        self.onNext = onNext
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // Options layout using FlexibleTagLayout - centered vertically
            VStack {
                Spacer()
                
                FlexibleTagLayout(
                    data: options,
                    spacing: 12
                ) { option in
                    CapsuleButton(
                        title: option,
                        isSelected: selectedOptions.contains(option)
                    ) {
                        toggleOption(option)
                    }
                }
                .padding(.horizontal, 20)
                
                Spacer()
            }
            
            // Next button - ADD: NavigationLink wrapper
            if title.contains("interests") {
                NavigationLink {
                    ConversationStyleView()
                } label: {
                    Text("Next")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .frame(maxWidth: .infinity)
                        .frame(height: 52)
                        .foregroundStyle(.black)
                        .background(.accent)
                        .clipShape(.capsule)
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 40)
                .opacity(selectedOptions.isEmpty ? 0.5 : 1.0)
                .disabled(selectedOptions.isEmpty)
            } else {
                Button("Next") {
                    handleNext()
                }
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity)
                .frame(height: 52)
                .foregroundStyle(.black)
                .background(.accent)
                .clipShape(.capsule)
                .padding(.horizontal, 20)
                .padding(.bottom, 40)
                .opacity(selectedOptions.isEmpty ? 0.5 : 1.0)
                .disabled(selectedOptions.isEmpty)
            }
        }
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("Skip") {
                    dismiss()
                }
                .foregroundStyle(.accent)
            }
        }
    }
    
    private func toggleOption(_ option: String) {
        withAnimation(.easeInOut(duration: 0.2)) {
            if allowMultipleSelection {
                if selectedOptions.contains(option) {
                    selectedOptions.remove(option)
                } else {
                    selectedOptions.insert(option)
                }
            } else {
                if selectedOptions.contains(option) {
                    selectedOptions.remove(option)
                } else {
                    selectedOptions.removeAll()
                    selectedOptions.insert(option)
                }
            }
        }
    }
    
    private func handleNext() {
        onNext(Array(selectedOptions))
    }
}

#Preview {
    NavigationStack {
        SelectionView(
            title: "Choose Options",
            options: ["Option 1", "Option 2", "Option 3"],
            allowMultipleSelection: true
        ) { selectedOptions in
            print("Selected: \(selectedOptions)")
        }
        .navigationTitle("Test")
    }
}
