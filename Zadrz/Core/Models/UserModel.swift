//
//  UserModel.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import Foundation

// MARK: - User Model
struct UserModel: Identifiable, Hashable, Codable {
    let id: String
    let fullName: String
    let email: String
    var phoneNumber: String?
    var bio: String
    var profileImageUrl: String?
    var emailVerified: Bool
    
    init(
        id: String,
        fullName: String,
        email: String,
        phoneNumber: String? = nil,
        bio: String = "Hey there! I am using Zadrz.",
        profileImageUrl: String? = nil,
        emailVerified: Bool = true
    ) {
        self.id = id
        self.fullName = fullName
        self.email = email
        self.phoneNumber = phoneNumber
        self.bio = bio
        self.profileImageUrl = profileImageUrl
        self.emailVerified = emailVerified
    }
}

// MARK: - User Model Extensions
extension UserModel {
    var initials: String {
        let components = fullName.components(separatedBy: " ")
        let firstInitial = components.first?.first?.uppercased() ?? ""
        let lastInitial = components.count > 1 ? components.last?.first?.uppercased() ?? "" : ""
        return firstInitial + lastInitial
    }
    
    var displayName: String {
        fullName.isEmpty ? "Unknown User" : fullName
    }
}

// MARK: - Sample Data
extension UserModel {
    static let sample = UserModel(
        id: "sample-user-id",
        fullName: "John Doe",
        email: "<EMAIL>",
        phoneNumber: "+1234567890",
        bio: "iOS Developer passionate about SwiftUI",
        profileImageUrl: nil,
        emailVerified: true
    )
}
