//
//  ChatModels.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import Foundation

// MARK: - Chat Item Model
struct ChatItem: Identifiable, Hashable {
    let id = UUID()
    let name: String
    let message: String
    let time: String
    let unreadCount: Int
    let isRead: Bool
    
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    static func == (lhs: ChatItem, rhs: ChatItem) -> Bool {
        lhs.id == rhs.id
    }
}

// MARK: - Chat Message Model
struct ChatMessage: Identifiable {
    let id = UUID()
    let text: String
    let isFromUser: Bool
    let timestamp: String
    let messageType: MessageType
    
    init(text: String, isFromUser: Bool, timestamp: String, messageType: MessageType = .text) {
        self.text = text
        self.isFromUser = isFromUser
        self.timestamp = timestamp
        self.messageType = messageType
    }
}

// MARK: - Message Type
enum MessageType {
    case text, file, image, date
}

// MARK: - Sample Data
extension ChatItem {
    static let sampleChats = [
        ChatItem(name: "<PERSON><PERSON>", message: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do", time: "22:30", unreadCount: 10, isRead: false),
        ChatItem(name: "John Smith", message: "Hey there! How are you doing?", time: "21:45", unreadCount: 2, isRead: false),
        ChatItem(name: "Sarah <PERSON>", message: "Thanks for the help yesterday", time: "20:15", unreadCount: 0, isRead: true),
        ChatItem(name: "Mike Johnson", message: "See you tomorrow at the meeting", time: "19:30", unreadCount: 0, isRead: true),
        ChatItem(name: "Emma Davis", message: "Great work on the project!", time: "18:45", unreadCount: 1, isRead: false),
        ChatItem(name: "Alex Brown", message: "Can you send me the files?", time: "17:20", unreadCount: 0, isRead: true),
        ChatItem(name: "Lisa Garcia", message: "Happy birthday! 🎉", time: "16:10", unreadCount: 0, isRead: true)
    ]
}

extension ChatMessage {
    static let sampleMessages: [ChatMessage] = [
        ChatMessage(text: "dolor sit", isFromUser: true, timestamp: "22:30"),
        ChatMessage(text: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt", isFromUser: false, timestamp: "22:30"),
        ChatMessage(text: "Lorem ipsum dolor sit amet", isFromUser: true, timestamp: "22:30"),
        ChatMessage(text: "document_name.doc", isFromUser: false, timestamp: "22:30", messageType: .file),
        ChatMessage(text: "July 5", isFromUser: false, timestamp: "", messageType: .date),
        ChatMessage(text: "Dolore magna aliqua", isFromUser: false, timestamp: "22:30", messageType: .image),
        ChatMessage(text: "See older messages...", isFromUser: false, timestamp: "22:30")
    ]
}
