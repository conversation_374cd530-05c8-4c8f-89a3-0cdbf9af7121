//
//  Constants.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import Foundation
import SwiftUI

// MARK: - App Constants
enum AppConstants {
    
    // MARK: - UI Constants
    enum UI {
        static let cornerRadius: CGFloat = 12
        static let buttonHeight: CGFloat = 52
        static let padding: CGFloat = 20
        static let smallPadding: CGFloat = 8
        static let largePadding: CGFloat = 32
        
        enum Animation {
            static let defaultDuration: Double = 0.3
            static let fastDuration: Double = 0.2
            static let slowDuration: Double = 0.5
        }
    }
    
    // MARK: - Text Constants
    enum Text {
        static let appName = "Zadrz"
        static let defaultBio = "Hey there! I am using Zadrz."
        
        enum Auth {
            static let signIn = "Sign In"
            static let signUp = "Sign Up"
            static let forgotPassword = "Forgot Password?"
            static let createAccount = "Create Account"
            static let alreadyHaveAccount = "Already have an account?"
            static let dontHaveAccount = "Don't have an account?"
        }
        
        enum Profile {
            static let profile = "Profile"
            static let editProfile = "Edit Profile"
            static let accountSettings = "Account Settings"
            static let logout = "Logout"
            static let deleteAccount = "Delete Account"
        }
        
        enum Chat {
            static let activeChats = "Active Chats"
            static let favoriteChats = "Favorite"
            static let savedChats = "Saved Chats"
        }
    }
    
    // MARK: - System Images
    enum SystemImages {
        static let person = "person"
        static let personFill = "person.fill"
        static let message = "message"
        static let messageFill = "message.fill"
        static let home = "house"
        static let homeFill = "house.fill"
        static let settings = "gearshape"
        static let settingsFill = "gearshape.fill"
        static let logout = "arrow.backward.square.fill"
        static let edit = "person.crop.circle.fill"
        static let checkmark = "checkmark"
        static let xmark = "xmark"
        static let xmarkCircleFill = "xmark.circle.fill"
        static let eye = "eye"
        static let eyeSlash = "eye.slash"
        static let arrowUp = "arrow.up"
        static let arrowUpCircleFill = "arrow.up.circle.fill"
        static let micFill = "mic.fill"
        static let paperclip = "paperclip"
        static let plus = "plus"
        static let plusCircleFill = "plus.circle.fill"
    }
    
    // MARK: - Validation
    enum Validation {
        static let minPasswordLength = 6
        static let maxBioLength = 150
        static let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
    }
}

// MARK: - Tab Configuration
enum TabItem: String, CaseIterable {
    case chats, avatars, profile
    
    var title: String {
        switch self {
        case .chats: return "Chats"
        case .avatars: return "Avatars"
        case .profile: return "Profile"
        }
    }
    
    var icon: String {
        switch self {
        case .chats: return AppConstants.SystemImages.message
        case .avatars: return "person.2"
        case .profile: return AppConstants.SystemImages.person
        }
    }
}
