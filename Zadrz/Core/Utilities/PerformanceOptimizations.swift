//
//  PerformanceOptimizations.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI
import Combine

// MARK: - Lazy Loading View
struct LazyView<Content: View>: View {
    let build: () -> Content
    
    init(_ build: @autoclosure @escaping () -> Content) {
        self.build = build
    }
    
    var body: Content {
        build()
    }
}

// MARK: - Debounced State
@propertyWrapper
struct Debounced<Value>: DynamicProperty {
    @State private var currentValue: Value
    @State private var debouncedValue: Value
    private let delay: TimeInterval
    private let scheduler: DispatchQueue
    
    init(
        wrappedValue: Value,
        delay: TimeInterval = 0.3,
        scheduler: DispatchQueue = .main
    ) {
        self._currentValue = State(initialValue: wrappedValue)
        self._debouncedValue = State(initialValue: wrappedValue)
        self.delay = delay
        self.scheduler = scheduler
    }
    
    var wrappedValue: Value {
        get { currentValue }
        nonmutating set {
            currentValue = newValue
            
            // Cancel previous debounce
            scheduler.asyncAfter(deadline: .now() + delay) {
                if currentValue as? String == newValue as? String {
                    debouncedValue = newValue
                }
            }
        }
    }
    
    var projectedValue: Value {
        debouncedValue
    }
}

// MARK: - Memory Efficient Image Cache
final class ImageCache {
    static let shared = ImageCache()
    
    private let cache = NSCache<NSString, UIImage>()
    private let maxMemoryUsage: Int = 50 * 1024 * 1024 // 50MB
    
    private init() {
        cache.totalCostLimit = maxMemoryUsage
        
        // Clear cache on memory warning
        NotificationCenter.default.addObserver(
            forName: UIApplication.didReceiveMemoryWarningNotification,
            object: nil,
            queue: .main
        ) { _ in
            self.clearCache()
        }
    }
    
    func setImage(_ image: UIImage, forKey key: String) {
        let cost = image.jpegData(compressionQuality: 0.8)?.count ?? 0
        cache.setObject(image, forKey: key as NSString, cost: cost)
    }
    
    func image(forKey key: String) -> UIImage? {
        return cache.object(forKey: key as NSString)
    }
    
    func clearCache() {
        cache.removeAllObjects()
    }
}

// MARK: - Optimized Async Image
struct OptimizedAsyncImage<Content: View, Placeholder: View>: View {
    let url: URL?
    let content: (Image) -> Content
    let placeholder: () -> Placeholder
    
    @State private var image: UIImage?
    @State private var isLoading = false
    
    init(
        url: URL?,
        @ViewBuilder content: @escaping (Image) -> Content,
        @ViewBuilder placeholder: @escaping () -> Placeholder
    ) {
        self.url = url
        self.content = content
        self.placeholder = placeholder
    }
    
    var body: some View {
        Group {
            if let image = image {
                content(Image(uiImage: image))
            } else {
                placeholder()
                    .onAppear {
                        loadImage()
                    }
            }
        }
    }
    
    private func loadImage() {
        guard let url = url, !isLoading else { return }
        
        let cacheKey = url.absoluteString
        
        // Check cache first
        if let cachedImage = ImageCache.shared.image(forKey: cacheKey) {
            self.image = cachedImage
            return
        }
        
        isLoading = true
        
        Task {
            do {
                let (data, _) = try await URLSession.shared.data(from: url)
                
                await MainActor.run {
                    if let uiImage = UIImage(data: data) {
                        ImageCache.shared.setImage(uiImage, forKey: cacheKey)
                        self.image = uiImage
                    }
                    isLoading = false
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                }
            }
        }
    }
}

// MARK: - View Modifiers for Performance
extension View {
    /// Prevents unnecessary view updates
    func equatable<T: Hashable>(_ value: T) -> some View {
        self.id(value)
    }
    
    /// Lazy loading for expensive views
    func lazyLoad() -> some View {
        LazyView(self)
    }
    
    /// Optimized for list performance
    func listRowOptimized() -> some View {
        self
            .listRowSeparator(.hidden)
            .listRowInsets(EdgeInsets())
            .buttonStyle(.plain)
    }
    
    /// Memory efficient scrolling
    func optimizedScrolling() -> some View {
        self
            .clipped()
            .drawingGroup() // Rasterize complex views
    }
}

// MARK: - Performance Monitoring
final class PerformanceMonitor {
    static let shared = PerformanceMonitor()
    
    private var startTime: CFAbsoluteTime = 0
    
    private init() {}
    
    func startMeasuring() {
        startTime = CFAbsoluteTimeGetCurrent()
    }
    
    func endMeasuring(operation: String) {
        let timeElapsed = CFAbsoluteTimeGetCurrent() - startTime
        print("⏱️ \(operation) took \(String(format: "%.3f", timeElapsed)) seconds")
    }
    
    func measureMemoryUsage() {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4

        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            let memoryUsage = Double(info.resident_size) / 1024.0 / 1024.0
            print("📱 Memory usage: \(String(format: "%.2f", memoryUsage)) MB")
        }
    }
}
