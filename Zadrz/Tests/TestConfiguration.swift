//
//  TestConfiguration.swift
//  ZadrzTests
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import Testing
import Foundation

// MARK: - Global Test Configuration

/// Global test configuration for the Zadrz test suite
/// This file contains shared test utilities, configurations, and setup
struct TestConfiguration {
    
    // MARK: - Test Environment
    
    /// Checks if we're running in CI environment
    static var isRunningInCI: Bool {
        ProcessInfo.processInfo.environment["CI"] == "true"
    }
    
    /// Checks if performance tests are enabled
    static var performanceTestsEnabled: Bool {
        ProcessInfo.processInfo.environment["ENABLE_PERFORMANCE_TESTS"] == "true"
    }
    
    /// Checks if integration tests are enabled
    static var integrationTestsEnabled: Bool {
        ProcessInfo.processInfo.environment["ENABLE_INTEGRATION_TESTS"] == "true"
    }
    
    // MARK: - Test Data
    
    /// Sample test users for testing
    static let testUsers = [
        UserModel(id: "test1", fullName: "<PERSON>", email: "<EMAIL>"),
        User<PERSON>odel(id: "test2", fullName: "<PERSON>", email: "<EMAIL>"),
        UserModel(id: "test3", fullName: "<PERSON>", email: "<EMAIL>")
    ]
    
    /// Sample test chats for testing
    static let testChats = [
        ChatItem(name: "Test Chat 1", message: "Hello", time: "12:00", unreadCount: 1, isRead: false),
        ChatItem(name: "Test Chat 2", message: "Hi there", time: "11:30", unreadCount: 0, isRead: true),
        ChatItem(name: "Test Chat 3", message: "How are you?", time: "11:00", unreadCount: 2, isRead: false)
    ]
    
    // MARK: - Test Utilities
    
    /// Creates a temporary directory for test files
    static func createTemporaryDirectory() throws -> URL {
        let tempDir = FileManager.default.temporaryDirectory
        let testDir = tempDir.appendingPathComponent("ZadrzTests-\(UUID().uuidString)")
        try FileManager.default.createDirectory(at: testDir, withIntermediateDirectories: true)
        return testDir
    }
    
    /// Cleans up temporary test files
    static func cleanupTemporaryDirectory(_ url: URL) {
        try? FileManager.default.removeItem(at: url)
    }
    
    /// Waits for a condition to be true with timeout
    static func waitForCondition(
        timeout: TimeInterval = 5.0,
        condition: @escaping () -> Bool
    ) async throws {
        let startTime = Date()
        
        while !condition() {
            if Date().timeIntervalSince(startTime) > timeout {
                throw TestError.timeout
            }
            try await Task.sleep(nanoseconds: 10_000_000) // 10ms
        }
    }
    
    /// Measures execution time of a block
    static func measureTime<T>(
        operation: () async throws -> T
    ) async rethrows -> (result: T, duration: TimeInterval) {
        let startTime = Date()
        let result = try await operation()
        let endTime = Date()
        let duration = endTime.timeIntervalSince(startTime)
        return (result, duration)
    }
}

// MARK: - Test Errors

enum TestError: Error, LocalizedError {
    case timeout
    case invalidTestData
    case mockServiceError
    
    var errorDescription: String? {
        switch self {
        case .timeout:
            return "Test operation timed out"
        case .invalidTestData:
            return "Invalid test data provided"
        case .mockServiceError:
            return "Mock service encountered an error"
        }
    }
}

// MARK: - Test Assertions

/// Custom test assertions for better error messages
struct TestAssertions {
    
    /// Asserts that two arrays contain the same elements (order independent)
    static func expectSameElements<T: Equatable>(
        _ actual: [T],
        _ expected: [T],
        file: StaticString = #file,
        line: UInt = #line
    ) {
        let actualSet = Set(actual)
        let expectedSet = Set(expected)
        
        withKnownIssue {
            #expect(actualSet == expectedSet, "Arrays don't contain the same elements")
        }
    }
    
    /// Asserts that a value is within a range
    static func expectInRange<T: Comparable>(
        _ value: T,
        _ range: ClosedRange<T>,
        file: StaticString = #file,
        line: UInt = #line
    ) {
        #expect(range.contains(value), "Value \(value) is not in range \(range)")
    }
    
    /// Asserts that an async operation completes within a time limit
    static func expectCompletesWithin<T>(
        _ timeLimit: TimeInterval,
        operation: () async throws -> T,
        file: StaticString = #file,
        line: UInt = #line
    ) async throws -> T {
        let (result, duration) = try await TestConfiguration.measureTime(operation: operation)
        #expect(duration <= timeLimit, "Operation took \(duration)s, expected ≤ \(timeLimit)s")
        return result
    }
}

// MARK: - Mock Data Generators

struct MockDataGenerator {
    
    /// Generates random chat items for testing
    static func generateChatItems(count: Int) -> [ChatItem] {
        return (0..<count).map { index in
            ChatItem(
                name: "User \(index)",
                message: generateRandomMessage(),
                time: generateRandomTime(),
                unreadCount: Int.random(in: 0...5),
                isRead: Bool.random()
            )
        }
    }
    
    /// Generates random chat messages for testing
    static func generateChatMessages(count: Int) -> [ChatMessage] {
        return (0..<count).map { index in
            ChatMessage(
                text: "Message \(index): \(generateRandomMessage())",
                isFromUser: Bool.random(),
                timestamp: generateRandomTime(),
                messageType: MessageType.allCases.randomElement() ?? .text
            )
        }
    }
    
    /// Generates random user models for testing
    static func generateUsers(count: Int) -> [UserModel] {
        let firstNames = ["John", "Jane", "Bob", "Alice", "Charlie", "Diana"]
        let lastNames = ["Doe", "Smith", "Johnson", "Brown", "Davis", "Wilson"]
        
        return (0..<count).map { index in
            let firstName = firstNames.randomElement() ?? "User"
            let lastName = lastNames.randomElement() ?? "Test"
            return UserModel(
                id: "user_\(index)",
                fullName: "\(firstName) \(lastName)",
                email: "\(firstName.lowercased()).\(lastName.lowercased())@test.com"
            )
        }
    }
    
    // MARK: - Private Helpers
    
    private static func generateRandomMessage() -> String {
        let messages = [
            "Hello there!",
            "How are you doing?",
            "This is a test message",
            "Lorem ipsum dolor sit amet",
            "Great to hear from you",
            "Let's chat about this",
            "Interesting point you made",
            "I completely agree",
            "Thanks for sharing",
            "Looking forward to it"
        ]
        return messages.randomElement() ?? "Test message"
    }
    
    private static func generateRandomTime() -> String {
        let hour = Int.random(in: 0...23)
        let minute = Int.random(in: 0...59)
        return String(format: "%02d:%02d", hour, minute)
    }
}

// MARK: - MessageType Extension

extension MessageType: CaseIterable {
    public static var allCases: [MessageType] {
        return [.text, .file, .image, .date]
    }
}
