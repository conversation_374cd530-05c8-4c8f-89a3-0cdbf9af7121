//
//  AuthenticationViewModelTests.swift
//  ZadrzTests
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import Testing
import Combine
@testable import Zadrz

@MainActor
@Suite("Authentication View Model Tests")
struct AuthenticationViewModelTests {

    // MARK: - Test Setup

    func createTestViewModel() -> (AuthenticationViewModel, MockAuthenticationService) {
        let mockAuthService = MockAuthenticationService()
        let viewModel = AuthenticationViewModel(authService: mockAuthService)
        return (viewModel, mockAuthService)
    }
    
    // MARK: - Sign Up Tests

    @Test("Sign up with valid data succeeds")
    func signUpWithValidData() async {
        // Given
        let (viewModel, mockAuthService) = createTestViewModel()
        viewModel.email = "<EMAIL>"
        viewModel.fullName = "Test User"
        viewModel.password = "password123"

        mockAuthService.shouldSucceed = true

        // When
        await viewModel.signUp()

        // Then
        #expect(mockAuthService.createAccountCalled)
        #expect(mockAuthService.lastEmail == "<EMAIL>")
        #expect(mockAuthService.lastFullName == "Test User")
        #expect(mockAuthService.lastPassword == "password123")
        #expect(!viewModel.isSignUpLoading)
        #expect(viewModel.successMessage != nil)
    }

    @Test("Sign up with invalid email fails")
    func signUpWithInvalidEmail() async {
        // Given
        let (viewModel, mockAuthService) = createTestViewModel()
        viewModel.email = "invalid-email"
        viewModel.fullName = "Test User"
        viewModel.password = "password123"

        // When
        await viewModel.signUp()

        // Then
        #expect(!mockAuthService.createAccountCalled)
        #expect(viewModel.errorMessage != nil)
        #expect(viewModel.errorMessage?.contains("valid email") == true)
    }

    @Test("Sign up with short password fails")
    func signUpWithShortPassword() async {
        // Given
        let (viewModel, mockAuthService) = createTestViewModel()
        viewModel.email = "<EMAIL>"
        viewModel.fullName = "Test User"
        viewModel.password = "123"

        // When
        await viewModel.signUp()

        // Then
        #expect(!mockAuthService.createAccountCalled)
        #expect(viewModel.errorMessage != nil)
        #expect(viewModel.errorMessage?.contains("6 characters") == true)
    }
    
    // MARK: - Sign In Tests

    @Test("Sign in with valid credentials succeeds")
    func signInWithValidCredentials() async {
        // Given
        let (viewModel, mockAuthService) = createTestViewModel()
        viewModel.email = "<EMAIL>"
        viewModel.password = "password123"

        mockAuthService.shouldSucceed = true

        // When
        let result = await viewModel.signIn()

        // Then
        #expect(result == true)
        #expect(mockAuthService.loginCalled)
        #expect(!viewModel.isEmailSignInLoading)
        #expect(viewModel.successMessage != nil)
    }

    @Test("Sign in with invalid credentials fails")
    func signInWithInvalidCredentials() async {
        // Given
        let (viewModel, mockAuthService) = createTestViewModel()
        viewModel.email = "<EMAIL>"
        viewModel.password = "wrongpassword"

        mockAuthService.shouldSucceed = false
        mockAuthService.errorToThrow = AuthenticationError.invalidCredentials

        // When
        let result = await viewModel.signIn()

        // Then
        #expect(result == false)
        #expect(mockAuthService.loginCalled)
        #expect(viewModel.errorMessage != nil)
    }
    
    // MARK: - Reset Password Tests

    @Test("Reset password with valid email succeeds")
    func resetPasswordWithValidEmail() async {
        // Given
        let (viewModel, mockAuthService) = createTestViewModel()
        viewModel.email = "<EMAIL>"
        mockAuthService.shouldSucceed = true

        // When
        await viewModel.resetPassword()

        // Then
        #expect(mockAuthService.resetPasswordCalled)
        #expect(viewModel.successMessage != nil)
    }

    @Test("Reset password with invalid email fails")
    func resetPasswordWithInvalidEmail() async {
        // Given
        let (viewModel, mockAuthService) = createTestViewModel()
        viewModel.email = "invalid-email"

        // When
        await viewModel.resetPassword()

        // Then
        #expect(!mockAuthService.resetPasswordCalled)
        #expect(viewModel.errorMessage != nil)
    }
}

// MARK: - Parameterized Tests

@Suite("Authentication Validation Tests")
struct AuthenticationValidationTests {

    @Test("Email validation", arguments: [
        ("<EMAIL>", true),
        ("<EMAIL>", true),
        ("invalid-email", false),
        ("@invalid.com", false),
        ("invalid@", false),
        ("", false)
    ])
    func emailValidation(email: String, expectedValid: Bool) {
        let isValid = email.isValidEmail
        #expect(isValid == expectedValid)
    }

    @Test("Password validation", arguments: [
        ("password123", true),
        ("123456", true),
        ("12345", false),
        ("", false),
        ("a", false)
    ])
    func passwordValidation(password: String, expectedValid: Bool) {
        let isValid = password.isValidPassword
        #expect(isValid == expectedValid)
    }
}

// MARK: - Mock Authentication Service

final class MockAuthenticationService: AuthenticationServiceProtocol {
    var authState = CurrentValueSubject<AuthenticationState, Never>(.pending)
    var currentUser: User? = nil
    
    var shouldSucceed = true
    var errorToThrow: Error = AuthenticationError.unknownError
    
    // Tracking properties
    var createAccountCalled = false
    var loginCalled = false
    var resetPasswordCalled = false
    var logoutCalled = false
    
    var lastEmail: String?
    var lastFullName: String?
    var lastPassword: String?
    
    func autoLogin() async {
        authState.send(shouldSucceed ? .authenticated : .unauthenticated)
    }
    
    func login(email: String, password: String) async throws {
        loginCalled = true
        lastEmail = email
        lastPassword = password
        
        if shouldSucceed {
            authState.send(.authenticated)
        } else {
            throw errorToThrow
        }
    }
    
    func createAccount(email: String, fullName: String, password: String) async throws {
        createAccountCalled = true
        lastEmail = email
        lastFullName = fullName
        lastPassword = password
        
        if shouldSucceed {
            authState.send(.authenticated)
        } else {
            throw errorToThrow
        }
    }
    
    func logout() async throws {
        logoutCalled = true
        
        if shouldSucceed {
            authState.send(.unauthenticated)
        } else {
            throw errorToThrow
        }
    }
    
    func resetPassword(email: String) async throws {
        resetPasswordCalled = true
        lastEmail = email
        
        if !shouldSucceed {
            throw errorToThrow
        }
    }
    
    func updatePassword(currentPassword: String, newPassword: String) async throws {
        if !shouldSucceed {
            throw errorToThrow
        }
    }
    
    func updateEmail(currentPassword: String, newEmail: String) async throws {
        if !shouldSucceed {
            throw errorToThrow
        }
    }
    
    func deleteAccount(password: String) async throws {
        if !shouldSucceed {
            throw errorToThrow
        }
    }
    
    func getCurrentUserData() async throws -> UserModel? {
        if shouldSucceed {
            return UserModel.sample
        } else {
            throw errorToThrow
        }
    }
    
    func updateUserInDatabase(_ updates: [String: Any]) async throws {
        if !shouldSucceed {
            throw errorToThrow
        }
    }
    
    func resendEmailVerification() async throws {
        if !shouldSucceed {
            throw errorToThrow
        }
    }
}
