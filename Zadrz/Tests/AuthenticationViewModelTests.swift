//
//  AuthenticationViewModelTests.swift
//  ZadrzTests
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import XCTest
import Combine
@testable import Zadrz

@MainActor
final class AuthenticationViewModelTests: XCTestCase {
    
    var viewModel: AuthenticationViewModel!
    var mockAuthService: MockAuthenticationService!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() {
        super.setUp()
        mockAuthService = MockAuthenticationService()
        viewModel = AuthenticationViewModel(authService: mockAuthService)
        cancellables = Set<AnyCancellable>()
    }
    
    override func tearDown() {
        viewModel = nil
        mockAuthService = nil
        cancellables = nil
        super.tearDown()
    }
    
    // MARK: - Sign Up Tests
    
    func testSignUpWithValidData() async {
        // Given
        viewModel.email = "<EMAIL>"
        viewModel.fullName = "Test User"
        viewModel.password = "password123"
        
        mockAuthService.shouldSucceed = true
        
        // When
        await viewModel.signUp()
        
        // Then
        XCTAssertTrue(mockAuthService.createAccountCalled)
        XCTAssertEqual(mockAuthService.lastEmail, "<EMAIL>")
        XCTAssertEqual(mockAuthService.lastFullName, "Test User")
        XCTAssertEqual(mockAuthService.lastPassword, "password123")
        XCTAssertFalse(viewModel.isSignUpLoading)
        XCTAssertNotNil(viewModel.successMessage)
    }
    
    func testSignUpWithInvalidEmail() async {
        // Given
        viewModel.email = "invalid-email"
        viewModel.fullName = "Test User"
        viewModel.password = "password123"
        
        // When
        await viewModel.signUp()
        
        // Then
        XCTAssertFalse(mockAuthService.createAccountCalled)
        XCTAssertNotNil(viewModel.errorMessage)
        XCTAssertTrue(viewModel.errorMessage?.contains("valid email") ?? false)
    }
    
    func testSignUpWithShortPassword() async {
        // Given
        viewModel.email = "<EMAIL>"
        viewModel.fullName = "Test User"
        viewModel.password = "123"
        
        // When
        await viewModel.signUp()
        
        // Then
        XCTAssertFalse(mockAuthService.createAccountCalled)
        XCTAssertNotNil(viewModel.errorMessage)
        XCTAssertTrue(viewModel.errorMessage?.contains("6 characters") ?? false)
    }
    
    // MARK: - Sign In Tests
    
    func testSignInWithValidCredentials() async {
        // Given
        viewModel.email = "<EMAIL>"
        viewModel.password = "password123"
        
        mockAuthService.shouldSucceed = true
        
        // When
        let result = await viewModel.signIn()
        
        // Then
        XCTAssertTrue(result)
        XCTAssertTrue(mockAuthService.loginCalled)
        XCTAssertFalse(viewModel.isEmailSignInLoading)
        XCTAssertNotNil(viewModel.successMessage)
    }
    
    func testSignInWithInvalidCredentials() async {
        // Given
        viewModel.email = "<EMAIL>"
        viewModel.password = "wrongpassword"
        
        mockAuthService.shouldSucceed = false
        mockAuthService.errorToThrow = AuthenticationError.invalidCredentials
        
        // When
        let result = await viewModel.signIn()
        
        // Then
        XCTAssertFalse(result)
        XCTAssertTrue(mockAuthService.loginCalled)
        XCTAssertNotNil(viewModel.errorMessage)
    }
    
    // MARK: - Reset Password Tests
    
    func testResetPasswordWithValidEmail() async {
        // Given
        viewModel.email = "<EMAIL>"
        mockAuthService.shouldSucceed = true
        
        // When
        await viewModel.resetPassword()
        
        // Then
        XCTAssertTrue(mockAuthService.resetPasswordCalled)
        XCTAssertNotNil(viewModel.successMessage)
    }
    
    func testResetPasswordWithInvalidEmail() async {
        // Given
        viewModel.email = "invalid-email"
        
        // When
        await viewModel.resetPassword()
        
        // Then
        XCTAssertFalse(mockAuthService.resetPasswordCalled)
        XCTAssertNotNil(viewModel.errorMessage)
    }
}

// MARK: - Mock Authentication Service

final class MockAuthenticationService: AuthenticationServiceProtocol {
    var authState = CurrentValueSubject<AuthenticationState, Never>(.pending)
    var currentUser: User? = nil
    
    var shouldSucceed = true
    var errorToThrow: Error = AuthenticationError.unknownError
    
    // Tracking properties
    var createAccountCalled = false
    var loginCalled = false
    var resetPasswordCalled = false
    var logoutCalled = false
    
    var lastEmail: String?
    var lastFullName: String?
    var lastPassword: String?
    
    func autoLogin() async {
        authState.send(shouldSucceed ? .authenticated : .unauthenticated)
    }
    
    func login(email: String, password: String) async throws {
        loginCalled = true
        lastEmail = email
        lastPassword = password
        
        if shouldSucceed {
            authState.send(.authenticated)
        } else {
            throw errorToThrow
        }
    }
    
    func createAccount(email: String, fullName: String, password: String) async throws {
        createAccountCalled = true
        lastEmail = email
        lastFullName = fullName
        lastPassword = password
        
        if shouldSucceed {
            authState.send(.authenticated)
        } else {
            throw errorToThrow
        }
    }
    
    func logout() async throws {
        logoutCalled = true
        
        if shouldSucceed {
            authState.send(.unauthenticated)
        } else {
            throw errorToThrow
        }
    }
    
    func resetPassword(email: String) async throws {
        resetPasswordCalled = true
        lastEmail = email
        
        if !shouldSucceed {
            throw errorToThrow
        }
    }
    
    func updatePassword(currentPassword: String, newPassword: String) async throws {
        if !shouldSucceed {
            throw errorToThrow
        }
    }
    
    func updateEmail(currentPassword: String, newEmail: String) async throws {
        if !shouldSucceed {
            throw errorToThrow
        }
    }
    
    func deleteAccount(password: String) async throws {
        if !shouldSucceed {
            throw errorToThrow
        }
    }
    
    func getCurrentUserData() async throws -> UserModel? {
        if shouldSucceed {
            return UserModel.sample
        } else {
            throw errorToThrow
        }
    }
    
    func updateUserInDatabase(_ updates: [String: Any]) async throws {
        if !shouldSucceed {
            throw errorToThrow
        }
    }
    
    func resendEmailVerification() async throws {
        if !shouldSucceed {
            throw errorToThrow
        }
    }
}
