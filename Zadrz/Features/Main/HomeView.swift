//
//  HomeView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

struct HomeView: View {
    
    @ObservedObject var authVM: AuthScreenViewModel = .init()
    
    var body: some View {
        VStack(spacing: 20) {
            Text("Home View")
                .font(.title)
            
            NavigationLink("Go to Profile") {
                ProfileView(authVM: authVM)
            }
        }
        .navigationTitle("Home")
        .navigationDestination(for: String.self) { destination in
            if destination == "profile" {
                ProfileView(authVM: authVM)
            }
        }
    }
}

#Preview {
    NavigationStack {
        HomeView()
    }
}
