import SwiftUI

struct LoginView: View {
    @ObservedObject var authVM: AuthScreenViewModel
    var onAuthenticated: (() -> Void)? = nil
    @FocusState private var focusedField: Field?
    @State private var navigateToInterests = false
    @State private var showValidationAlert = false
    @State private var validationMessage = ""
    @State private var showForgotPassword = false
    
    var body: some View {
        VStack(spacing: 0) {
            LoginSheet(
                email: $authVM.email,
                password: $authVM.password,
                focusedField: $focusedField,
                isLoading: authVM.isEmailSignInLoading,
                onLogin: handleLogin,
                onForgotPassword: { showForgotPassword = true }
            )
        }
        .onTapGesture {
            focusedField = nil
        }
        .onAppear {
            authVM.clearSignInFieldsForView()
        }
        .task {
            focusedField = .email
        }
        .navigationDestination(isPresented: $navigateToInterests) {
            InterestsSelectionView()
        }
        .sheet(isPresented: $showForgotPassword) {
            NavigationStack {
                ForgotPasswordView(authVM: authVM)
            }
        }
        .alert("Validation Error", isPresented: $showValidationAlert) {
            Button("OK", role: .cancel) {}
        } message: {
            Text(validationMessage)
        }
        .sensoryFeedback(.success, trigger: authVM.successTrigger)
        .sensoryFeedback(.error, trigger: authVM.errorTrigger)
        .alert("Success", isPresented: $authVM.showSuccessAlert) {
            Button("Continue") {
                authVM.clearSignInFieldsForView()
                onAuthenticated?()
                navigateToInterests = true
            }
        } message: {
            Text(authVM.successMessage)
        }
        .alert("Error", isPresented: $authVM.errorState.showError) {
            Button("OK", role: .cancel) {
                authVM.errorState = (false, "")
            }
        } message: {
            Text(authVM.errorState.errorMessage)
        }
        .navigationBarTitleDisplayMode(.large)
        .navigationBarTitle("Login")
    }
    
    private func handleLogin() {
        focusedField = nil
        
        // Field validation before sending to Firebase
        if authVM.email.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            validationMessage = "Please enter your email."
            showValidationAlert = true
            return
        }
        if authVM.password.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            validationMessage = "Please enter your password."
            showValidationAlert = true
            return
        }
        Task {
            await authVM.handleSignIn()
        }
    }
}

private struct LoginSheet: View {
    @Binding var email: String
    @Binding var password: String
    @FocusState.Binding var focusedField: Field?
    let isLoading: Bool
    let onLogin: () -> Void
    let onForgotPassword: () -> Void
    
    var body: some View {
        VStack(spacing: 24) {
            VStack(spacing: 16) {
                AppTextField(
                    title: "E-mail",
                    placeholder: "<EMAIL>",
                    text: $email,
                    keyboardType: .emailAddress,
                    autocapitalization: .never,
                    onSubmit: {
                        focusedField = .password
                    }
                )
                .focused($focusedField, equals: .email)
                
                AppTextField(
                    title: "Password",
                    placeholder: "6-12 characters",
                    text: $password,
                    isSecure: true,
                    onSubmit: onLogin
                )
                .focused($focusedField, equals: .password)
                
                HStack {
                    Spacer()
                    Button("Forgot Password?") {
                        onForgotPassword()
                    }
                    .font(.subheadline)
                    .foregroundColor(.accentColor)
                }
            }
            .padding(.horizontal, 24)
            
            AppButton(
                title: "Login",
                action: onLogin,
                isLoading: isLoading
            )
            .padding(.horizontal, 24)
            .padding(.bottom, 32)
        }
    }
}

#Preview {
    NavigationStack {
        LoginView(authVM: AuthScreenViewModel())
    }
}
