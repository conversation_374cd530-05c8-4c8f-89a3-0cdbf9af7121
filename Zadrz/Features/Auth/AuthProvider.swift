//
//  AuthProvider.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/20/25.
//

import Foundation
import Combine
import FirebaseAuth
import FirebaseDatabase

enum AuthState {
    case pending, loggedIn, loggedOut
}

enum AuthError: Error, LocalizedError {
    case userNotLoggedIn
    case invalidEmail
    case invalidCredentials
    case networkError
    case weakPassword
    case emailVerificationRequired
    case unknownError
    
    var errorDescription: String? {
        switch self {
        case .userNotLoggedIn:
            return "You need to be logged in to perform this action."
        case .invalidEmail:
            return "Please provide a valid email address."
        case .invalidCredentials:
            return "The email or password is incorrect."
        case .networkError:
            return "Network error. Please check your connection."
        case .weakPassword:
            return "Password should be at least 6 characters long."
        case .emailVerificationRequired:
            return "A verification email has been sent to your new email address. Please verify it to complete the email change."
        case .unknownError:
            return "An unknown error occurred."
        }
    }
}

protocol AuthProvider {
    static var shared: AuthProvider { get }
    var authState: CurrentValueSubject<AuthState, Never> { get }
    var currentUser: User? { get }
    
    func autoLogin() async
    func login(with email: String, and password: String) async throws
    func createAccount(for email: String, fullName: String, password: String) async throws
    func logout() async throws
    func resetPassword(for email: String) async throws
    func updatePassword(currentPassword: String, newPassword: String) async throws
    func updateEmail(currentPassword: String, newEmail: String) async throws
    func deleteAccount(password: String) async throws
    func getCurrentUserData() async throws -> UserItem?
    func updateUserInDatabase(_ updates: [String: Any]) async throws
    func resendEmailVerification() async throws
    func saveUserToDatabase(_ user: UserItem) async throws
}

final class AuthManager: AuthProvider {
    
    private init() { }
    
    static let shared: AuthProvider = AuthManager()
    
    var authState = CurrentValueSubject<AuthState, Never>(.pending)
    
    var currentUser: User? {
        return Auth.auth().currentUser
    }
    
    func autoLogin() async {
        // Check if user is already signed in
        if Auth.auth().currentUser != nil {
            authState.send(.loggedIn)
        } else {
            authState.send(.loggedOut)
        }
    }
    
    func login(with email: String, and password: String) async throws {
        try await Auth.auth().signIn(withEmail: email, password: password)
        authState.send(.loggedIn)
    }
    
    func createAccount(for email: String, fullName: String, password: String) async throws {
        let authResult = try await Auth.auth().createUser(withEmail: email, password: password)
        let id = authResult.user.uid
        
        let user = UserItem(
            id: id,
            fullName: fullName,
            email: email,
            password: password
        )
        
        try await saveUserToDatabase(user)
        authState.send(.loggedIn)
    }
    
    func logout() async throws {
        do {
            try Auth.auth().signOut()
            authState.send(.loggedOut)
        } catch {
            throw error
        }
    }
    
    func resetPassword(for email: String) async throws {
        try await Auth.auth().sendPasswordReset(withEmail: email)
    }
    
    func updateEmail(currentPassword: String, newEmail: String) async throws {
        guard let user = Auth.auth().currentUser, let email = user.email else {
            throw AuthError.userNotLoggedIn
        }
        
        // Reauthenticate the user first
        let credential = EmailAuthProvider.credential(withEmail: email, password: currentPassword)
        
        do {
            try await user.reauthenticate(with: credential)
            
            try await user.sendEmailVerification(beforeUpdatingEmail: newEmail)
            
            // Update email in database immediately
            // Note: Firebase Auth email will be updated after user verifies the new email
            try await updateUserInDatabase(["email": newEmail, "emailVerified": false])
            
        } catch {
            throw mapFirebaseError(error)
        }
    }

    func updatePassword(currentPassword: String, newPassword: String) async throws {
        guard let user = Auth.auth().currentUser, let email = user.email else {
            throw AuthError.userNotLoggedIn
        }
        
        // Reauthenticate the user first
        let credential = EmailAuthProvider.credential(withEmail: email, password: currentPassword)
        
        do {
            try await user.reauthenticate(with: credential)
            
            // Update password in Firebase Auth
            try await user.updatePassword(to: newPassword)
            
            // Note: We don't store passwords in database for security reasons
            
        } catch {
            throw mapFirebaseError(error)
        }
    }

    func deleteAccount(password: String) async throws {
        guard let user = Auth.auth().currentUser, let email = user.email else {
            throw AuthError.userNotLoggedIn
        }
        
        // Reauthenticate the user first
        let credential = EmailAuthProvider.credential(withEmail: email, password: password)
        
        do {
            try await user.reauthenticate(with: credential)
            
            // Delete user data from database
            try await Database.database().reference()
                .child("users")
                .child(user.uid)
                .removeValue()
            
            // Delete the user account
            try await user.delete()
            authState.send(.loggedOut)
        } catch {
            throw mapFirebaseError(error)
        }
    }
    
    func updateUserInDatabase(_ updates: [String: Any]) async throws {
        guard let currentUser = Auth.auth().currentUser else {
            throw AuthError.userNotLoggedIn
        }
        
        try await Database.database().reference()
            .child("users")
            .child(currentUser.uid)
            .updateChildValues(updates)
    }
    
    func resendEmailVerification() async throws {
        guard let user = Auth.auth().currentUser else {
            throw AuthError.userNotLoggedIn
        }
        
        do {
            try await user.sendEmailVerification()
        } catch {
            throw mapFirebaseError(error)
        }
    }

    func getCurrentUserData() async throws -> UserItem? {
        guard let currentUser = Auth.auth().currentUser else {
            throw AuthError.userNotLoggedIn
        }
        
        let snapshot = try await Database.database().reference()
            .child("users")
            .child(currentUser.uid)
            .getData()
        
        guard let value = snapshot.value as? [String: Any],
              let id = value["id"] as? String,
              let fullName = value["fullName"] as? String,
              let email = value["email"] as? String else {
            return nil
        }
        
        return UserItem(
            id: id,
            fullName: fullName,
            email: email,
            phoneNumber: value["phoneNumber"] as? String,
            bio: value["bio"] as? String ?? "Hey there! I am using Zadrz.",
            profileImageUrl: value["profileImageUrl"] as? String,
            password: "", // Don't store password for security
            emailVerified: value["emailVerified"] as? Bool ?? true
        )
    }
    
    private func mapFirebaseError(_ error: Error) -> Error {
        let nsError = error as NSError
        
        // Map Firebase error codes to our custom AuthError types
        switch nsError.code {
        case AuthErrorCode.userNotFound.rawValue,
             AuthErrorCode.invalidEmail.rawValue:
            return AuthError.invalidEmail
        case AuthErrorCode.wrongPassword.rawValue,
             AuthErrorCode.invalidCredential.rawValue:
            return AuthError.invalidCredentials
        case AuthErrorCode.networkError.rawValue:
            return AuthError.networkError
        case AuthErrorCode.weakPassword.rawValue:
            return AuthError.weakPassword
        default:
            return AuthError.unknownError
        }
    }
}

extension AuthManager {
    func saveUserToDatabase(_ user: UserItem) async throws {
        let userDictionary: [String: Any] = [
            "id": user.id,
            "fullName": user.fullName,
            "email": user.email,
            "profileImageUrl": user.profileImageUrl ?? "",
            "emailVerified": true, // Google users are pre-verified
            "createdAt": ServerValue.timestamp()
        ]
        try await Database.database().reference()
            .child("users")
            .child(user.id)
            .setValue(userDictionary)
    }
}

struct UserItem: Identifiable, Hashable, Decodable {
    let id: String
    let fullName: String
    let email: String
    var phoneNumber: String? = nil
    var bio: String? = "Hey there! I am using Zadrz."
    var profileImageUrl: String?
    var password: String
    var emailVerified: Bool = true
    
    init(id: String, fullName: String, email: String, phoneNumber: String? = nil, bio: String? = "Hey there! I am using Zadrz.", profileImageUrl: String? = nil, password: String, emailVerified: Bool = true) {
        self.id = id
        self.fullName = fullName
        self.email = email
        self.phoneNumber = phoneNumber
        self.bio = bio
        self.profileImageUrl = profileImageUrl
        self.password = password
        self.emailVerified = emailVerified
    }
}
