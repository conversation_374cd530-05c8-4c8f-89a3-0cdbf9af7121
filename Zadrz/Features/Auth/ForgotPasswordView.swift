import SwiftUI

struct ForgotPasswordView: View {
    @ObservedObject var authVM: AuthScreenViewModel
    @FocusState private var emailFieldFocused: Bool
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        VStack(spacing: 24) {
            Text("Reset Password")
                .font(.title2)
                .fontWeight(.semibold)
                .padding(.top)
            
            Text("Enter your email address and we'll send you instructions to reset your password.")
                .font(.subheadline)
                .foregroundStyle(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            AppTextField(
                title: "Email",
                placeholder: "<EMAIL>",
                text: $authVM.email,
                keyboardType: .emailAddress,
                autocapitalization: .never
            )
            .focused($emailFieldFocused)
            .padding(.horizontal, 24)
            
            AppButton(
                title: "Send Reset Link",
                action: handleResetPassword,
                backgroundColor: .accent,
                foregroundColor: .black,
                isLoading: authVM.isLoading
            )
            .padding(.horizontal, 24)
            
            Button("Cancel") {
                dismiss()
            }
            .padding(.top, 8)
            
            Spacer()
        }
        .onAppear {
            emailFieldFocused = true
        }
        .alert("Success", isPresented: $authVM.showSuccessAlert) {
            Button("OK") {
                dismiss()
            }
        } message: {
            Text(authVM.successMessage)
        }
        .alert("Error", isPresented: $authVM.errorState.showError) {
            Button("OK", role: .cancel) {}
        } message: {
            Text(authVM.errorState.errorMessage)
        }
    }
    
    private func handleResetPassword() {
        // Validate email
        if authVM.email.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            authVM.errorState.errorMessage = "Please enter your email address."
            authVM.errorState.showError = true
            emailFieldFocused = true
            return
        }
        
        // Check email format
        let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)
        if !emailPredicate.evaluate(with: authVM.email) {
            authVM.errorState.errorMessage = "Please enter a valid email address."
            authVM.errorState.showError = true
            emailFieldFocused = true
            return
        }
        
        Task {
            await authVM.handlePasswordReset()
        }
    }
}

#Preview {
    ForgotPasswordView(authVM: AuthScreenViewModel())
}
