//
//  SignUpView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

enum Field: Hashable {
    case fullName, phoneNumber, email, password
}

struct SignUpView: View {
    @FocusState private var focusedField: Field?
    @ObservedObject var authVM: AuthScreenViewModel
    @State private var navigateToInterests = false
    @State private var showValidationAlert = false
    @State private var validationMessage = ""
    
    var body: some View {
        VStack(spacing: 0) {
            SignUpSheet(
                fullName: $authVM.fullName,
                phoneNumber: $authVM.phoneNumber,
                email: $authVM.email,
                password: $authVM.password,
                focusedField: $focusedField,
                isLoading: authVM.isSignUpLoading,
                onSignUp: handleSignUp
            )
        }
        .onTapGesture {
            focusedField = nil
        }
        .onAppear {
            authVM.clearSignUpFieldsForView()
        }
        .task {
            focusedField = .fullName
        }
        .navigationDestination(isPresented: $navigateToInterests) {
            InterestsSelectionView()
        }
        .alert("Validation Error", isPresented: $showValidationAlert) {
            Button("OK", role: .cancel) {}
        } message: {
            Text(validationMessage)
        }
        .sensoryFeedback(.success, trigger: authVM.successTrigger)
        .sensoryFeedback(.error, trigger: authVM.errorTrigger)
        .alert("Success", isPresented: $authVM.showSuccessAlert) {
            Button("Continue") {
                authVM.clearSignUpFieldsForView()
                navigateToInterests = true
            }
        } message: {
            Text(authVM.successMessage)
        }
        .alert("Error", isPresented: $authVM.errorState.showError) {
            Button("OK", role: .cancel) {
                authVM.errorState = (false, "")
            }
        } message: {
            Text(authVM.errorState.errorMessage)
        }
    }
    
    private func handleSignUp() {
        focusedField = nil
        
        if authVM.fullName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            validationMessage = "Please enter your full name"
            showValidationAlert = true
            return
        }
        
        if authVM.email.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            validationMessage = "Please enter your email"
            showValidationAlert = true
            return
        }
        
        if !isValidEmail(authVM.email) {
            validationMessage = "Please enter a valid email address"
            showValidationAlert = true
            return
        }
        
        if authVM.password.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            validationMessage = "Please enter your password"
            showValidationAlert = true
            return
        }
        
        if authVM.password.count < 6 {
            validationMessage = "Password must be at least 6 characters"
            showValidationAlert = true
            return
        }
        
        Task {
            await authVM.handleSignUp()
        }
    }
    
    private func isValidEmail(_ email: String) -> Bool {
        let emailRegEx = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegEx)
        return emailPredicate.evaluate(with: email)
    }
}

struct SignUpSheet: View {
    @Binding var fullName: String
    @Binding var phoneNumber: String
    @Binding var email: String
    @Binding var password: String
    @FocusState.Binding var focusedField: Field?
    var isLoading: Bool
    let onSignUp: () -> Void
    
    var body: some View {
        VStack(spacing: 24) {
            VStack(spacing: 16) {
                AppTextField(
                    title: "Full name",
                    placeholder: "Name Surname",
                    text: $fullName,
                    showInfoButton: true,
                    infoAction: {
                        // Info action
                    },
                    onSubmit: {
                        focusedField = .email
                    }
                )
                .focused($focusedField, equals: .fullName)
                
                AppTextField(
                    title: "E-mail",
                    placeholder: "<EMAIL>",
                    text: $email,
                    keyboardType: .emailAddress,
                    autocapitalization: .never,
                    onSubmit: {
                        focusedField = .password
                    }
                )
                .focused($focusedField, equals: .email)
                
                AppTextField(
                    title: "Password",
                    placeholder: "6-12 characters",
                    text: $password,
                    isSecure: true,
                    onSubmit: {
                        onSignUp()
                    }
                )
                .focused($focusedField, equals: .password)
            }
            .padding(.horizontal, 24)
            
            AppButton(
                title: "Sign Up",
                action: onSignUp,
                isLoading: isLoading
            )
            .padding(.horizontal, 24)
            .padding(.bottom, 32)
        }
        .navigationTitle("Sign up")
    }
}

#Preview {
    NavigationStack {
        SignUpView(authVM: AuthScreenViewModel())
    }
}
