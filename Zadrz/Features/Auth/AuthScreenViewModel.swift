//
//  AuthScreenViewModel.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/20/25.
//

import Foundation
import Combine
import GoogleSignIn
import FirebaseAuth
import FirebaseCore

final class AuthScreenViewModel: ObservableObject {
    @Published var isAuthenticated = false
    @Published var errorMessage: String?
    @Published var email: String = ""
    @Published var fullName: String = ""
    @Published var password: String = ""
    @Published var phoneNumber: String = ""
    @Published var errorState: (showError: Bool, errorMessage: String) = (false, "Uh, oh")
    @Published var isLoading = false
    @Published var isGoogleSignInLoading = false
    @Published var isAppleSignInLoading = false
    @Published var isEmailSignInLoading = false
    @Published var isSignUpLoading = false
    @Published var successMessage: String = ""
    @Published var showSuccessAlert = false

    @Published var currentPassword: String = ""
    @Published var newEmail: String = ""
    @Published var newPassword: String = ""
    @Published var passwordConfirmation: String = ""
    @Published var didLogout = false

    @Published var currentUser: UserItem?
    
    @Published var successTrigger = false
    @Published var errorTrigger = false

    private var cancellables = Set<AnyCancellable>()

    init() {
        // Listen to auth state changes from AuthManager
        AuthManager.shared.authState
            .receive(on: DispatchQueue.main)
            .sink { [weak self] state in
                switch state {
                case .loggedIn:
                    self?.isAuthenticated = true
                    self?.didLogout = false
                    Task {
                        await self?.loadCurrentUser()
                    }
                case .loggedOut:
                    self?.isAuthenticated = false
                    self?.currentUser = nil
                case .pending:
                    break
                }
            }
            .store(in: &cancellables)
    }

    @MainActor
    func loadCurrentUser() async {
        do {
            currentUser = try await AuthManager.shared.getCurrentUserData()
            
            // Update local email property to match current user
            if let userEmail = currentUser?.email {
                email = userEmail
            }
        } catch {
            print("Failed to load user data: \(error.localizedDescription)")
        }
    }

    @MainActor
    func handleSignUp() async {
        isSignUpLoading = true
        isLoading = true
        errorState = (false, "")
        
        do {
            try await AuthManager.shared.createAccount(for: email, fullName: fullName, password: password)
            // Auth state will be updated via the Combine publisher
            
            successMessage = "Account created successfully! Welcome to Zadrz."
            showSuccessAlert = true
            triggerSuccessFeedback()
            clearSignUpFields()
        } catch {
            errorState = (true, "Failed to create account: \(error.localizedDescription)")
            triggerErrorFeedback()
        }
        isSignUpLoading = false
        isLoading = false
    }

    @MainActor
    @discardableResult
    func handleSignIn() async -> Bool {
        isEmailSignInLoading = true
        errorState = (false, "")
        
        do {
            try await AuthManager.shared.login(with: email, and: password)
            // Auth state will be updated via the Combine publisher
            
            successMessage = "Successfully signed in! Welcome back."
            showSuccessAlert = true
            triggerSuccessFeedback()
            
            isEmailSignInLoading = false
            return true
        } catch {
            errorState = (true, "Failed to sign in: \(error.localizedDescription)")
            triggerErrorFeedback()
            isEmailSignInLoading = false
            return false
        }
    }

    @MainActor
    func handlePasswordReset() async {
        isLoading = true
        errorState = (false, "")
        
        do {
            try await AuthManager.shared.resetPassword(for: email)
            successMessage = "Password reset email sent. Check your inbox."
            showSuccessAlert = true
            triggerSuccessFeedback()
        } catch {
            errorState = (true, "Failed to reset password: \(error.localizedDescription)")
            triggerErrorFeedback()
        }
        clearEmailFields()
        isLoading = false
    }

    @MainActor
    func handleUpdateEmail() async {
        isLoading = true
        errorState = (false, "")
        
        if currentPassword.isEmpty || newEmail.isEmpty {
            errorState = (true, "Please enter the current password and new email")
            triggerErrorFeedback()
            isLoading = false
            return
        }
        
        // Validate email format
        if !isValidEmail(newEmail) {
            errorState = (true, "New email is not valid")
            triggerErrorFeedback()
            isLoading = false
            return
        }
        
        do {
            try await AuthManager.shared.updateEmail(currentPassword: currentPassword, newEmail: newEmail)
            
            // Refresh local user data after successful update
            await loadCurrentUser()
            
            successMessage = "Verification email sent to \(newEmail). Please check your inbox and verify your new email address."
            showSuccessAlert = true
            clearEmailFields()
            triggerSuccessFeedback()
        } catch {
            errorState = (true, "Failed to update email: \(error.localizedDescription)")
            triggerErrorFeedback()
        }
        clearEmailFields()
        isLoading = false
    }

    @MainActor
    func handleUpdatePassword() async {
        isLoading = true
        errorState = (false, "")
        
        if currentPassword.isEmpty || newPassword.isEmpty || passwordConfirmation.isEmpty {
            errorState = (true, "All fields are required.")
            triggerErrorFeedback()
            isLoading = false
            return
        }
        
        if newPassword != passwordConfirmation {
            errorState = (true, "New passwords do not match.")
            triggerErrorFeedback()
            isLoading = false
            return
        }
        
        if newPassword.count < 6 {
            errorState = (true, "New password must be at least 6 characters.")
            triggerErrorFeedback()
            isLoading = false
            return
        }
        
        do {
            try await AuthManager.shared.updatePassword(currentPassword: currentPassword, newPassword: newPassword)
            
            // No need to refresh user data for password update since we don't store passwords
            
            successMessage = "Password updated successfully."
            showSuccessAlert = true
            clearPasswordFields()
            triggerSuccessFeedback()
        } catch {
            errorState = (true, "Failed to update password: \(error.localizedDescription)")
            triggerErrorFeedback()
        }
        clearEmailFields()
        isLoading = false
    }

    @MainActor
    func deleteAccount() async {
        isLoading = true
        errorState = (false, "")
        
        if currentPassword.isEmpty {
            errorState = (true, "Please enter your current password to delete account")
            triggerErrorFeedback()
            isLoading = false
            return
        }
        
        do {
            try await AuthManager.shared.deleteAccount(password: currentPassword)
            successMessage = "Account deleted successfully"
            showSuccessAlert = true
            currentPassword = ""
            
            // Clear local user data since account is deleted
            currentUser = nil
            triggerSuccessFeedback()
            
        } catch {
            errorState = (true, "Failed to delete account: \(error.localizedDescription)")
            triggerErrorFeedback()
        }
        clearEmailFields()
        isLoading = false
    }

    @MainActor
    func tryAutoLogin() async {
        await AuthManager.shared.autoLogin()
        // Auth state will be updated via the Combine publisher
    }

    @MainActor
    func logout() async {
        isLoading = true
        errorState = (false, "")
        
        do {
            try await AuthManager.shared.logout()
            didLogout = true
            triggerSuccessFeedback()
            // Auth state will be updated via the Combine publisher
        } catch {
            errorState = (true, "Failed to logout: \(error.localizedDescription)")
            triggerErrorFeedback()
        }
        isLoading = false
    }

    @MainActor
    func resendEmailVerification() async {
        isLoading = true
        errorState = (false, "")
        
        do {
            try await AuthManager.shared.resendEmailVerification()
            successMessage = "Verification email sent successfully. Please check your inbox."
            showSuccessAlert = true
            triggerSuccessFeedback()
            
        } catch {
            errorState = (true, "Failed to send verification email: \(error.localizedDescription)")
            triggerErrorFeedback()
        }
        clearEmailFields()
        isLoading = false
    }

    @MainActor
    func signInWithGoogle() async -> Bool {
        isGoogleSignInLoading = true
        errorState = (false, "")
        
        guard let clientID = FirebaseApp.app()?.options.clientID else {
            errorState = (true, "Firebase configuration error")
            triggerErrorFeedback()
            isGoogleSignInLoading = false
            return false
        }
        
        let config = GIDConfiguration(clientID: clientID)
        GIDSignIn.sharedInstance.configuration = config

        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first,
              let rootViewController = window.rootViewController else {
            errorState = (true, "Unable to get root view controller")
            triggerErrorFeedback()
            isGoogleSignInLoading = false
            return false
        }

        do {
            let userAuthentication = try await GIDSignIn.sharedInstance.signIn(withPresenting: rootViewController)

            let user = userAuthentication.user
            guard let idToken = user.idToken else {
                errorState = (true, "ID token missing")
                triggerErrorFeedback()
                isGoogleSignInLoading = false
                return false
            }
            let accessToken = user.accessToken

            let credential = GoogleAuthProvider.credential(withIDToken: idToken.tokenString,
                                                           accessToken: accessToken.tokenString)

            let result = try await Auth.auth().signIn(with: credential)
            let firebaseUser = result.user
            
            // Create or update user in database
            let userItem = UserItem(
                id: firebaseUser.uid,
                fullName: firebaseUser.displayName ?? "",
                email: firebaseUser.email ?? "",
                profileImageUrl: firebaseUser.photoURL?.absoluteString,
                password: "" // No password for OAuth users
            )
            
            try await AuthManager.shared.saveUserToDatabase(userItem)
            
            print("User \(firebaseUser.uid) signed in with email \(firebaseUser.email ?? "unknown")")
            triggerSuccessFeedback()
            isGoogleSignInLoading = false
            
            // Auth state will be updated via the Combine publisher
            return true
        } catch {
            print("Google Sign In Error: \(error.localizedDescription)")
            errorState = (true, "Google Sign In failed: \(error.localizedDescription)")
            triggerErrorFeedback()
            isGoogleSignInLoading = false
            return false
        }
    }

    @MainActor
    func signInWithApple() async -> Bool {
        isAppleSignInLoading = true
        errorState = (false, "")
        
        // TODO: Implement Apple Sign In
        // For now, just simulate loading
        try? await Task.sleep(nanoseconds: 2_000_000_000) // 2 seconds
        
        errorState = (true, "Apple Sign In not implemented yet")
        triggerErrorFeedback()
        isAppleSignInLoading = false
        return false
    }

    // MARK: - Private Helper Methods
    private func clearPasswordFields() {
        currentPassword = ""
        newPassword = ""
        passwordConfirmation = ""
    }
    
    private func clearEmailFields() {
        currentPassword = ""
        newEmail = ""
        email = ""
        password = ""
    }
    
    private func clearSignUpFields() {
        email = ""
        password = ""
        fullName = ""
        phoneNumber = ""
    }
    
    private func clearSignInFields() {
        email = ""
        password = ""
    }
    
    private func isValidEmail(_ email: String) -> Bool {
        let emailRegEx = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        return NSPredicate(format: "SELF MATCHES %@", emailRegEx).evaluate(with: email)
    }
    
    private func triggerSuccessFeedback() {
        successTrigger.toggle()
    }
    
    private func triggerErrorFeedback() {
        errorTrigger.toggle()
    }
    
    @MainActor
    func clearSignUpFieldsForView() {
        email = ""
        password = ""
        fullName = ""
        phoneNumber = ""
        showSuccessAlert = false
        successMessage = ""
        errorState = (false, "")
    }
    
    @MainActor
    func clearSignInFieldsForView() {
        email = ""
        password = ""
        showSuccessAlert = false
        successMessage = ""
        errorState = (false, "")
    }
}
