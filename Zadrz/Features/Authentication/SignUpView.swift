//
//  SignUpView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

struct SignUpView: View {
    @ObservedObject var authViewModel: AuthenticationViewModel
    var onAuthenticated: (() -> Void)? = nil
    
    @FocusState private var focusedField: SignUpField?
    @State private var navigateToInterests = false
    
    var body: some View {
        VStack(spacing: 0) {
            SignUpForm(
                authViewModel: authViewModel,
                focusedField: $focusedField,
                onSignUp: handleSignUp
            )
        }
        .onTapGesture {
            focusedField = nil
        }
        .onAppear {
            clearFields()
        }
        .task {
            focusedField = .fullName
        }
        .navigationDestination(isPresented: $navigateToInterests) {
            InterestsSelectionView()
        }
        .sensoryFeedback(
            success: $authViewModel.successTrigger,
            error: $authViewModel.errorTrigger
        )
        .alert("Success", isPresented: $authViewModel.showSuc<PERSON><PERSON>lert) {
            Button("Continue") {
                clearFields()
                onAuthenticated?()
                navigateToInterests = true
            }
        } message: {
            Text(authViewModel.successMessage ?? "")
        }
        .alert("Error", isPresented: $authViewModel.showErrorAlert) {
            Button("OK", role: .cancel) {}
        } message: {
            Text(authViewModel.errorMessage ?? "")
        }
        .navigationBarTitleDisplayMode(.large)
        .navigationTitle(AppConstants.Text.Auth.signUp)
    }
    
    // MARK: - Private Methods
    private func handleSignUp() {
        focusedField = nil
        Task {
            await authViewModel.signUp()
        }
    }
    
    private func clearFields() {
        authViewModel.email = ""
        authViewModel.password = ""
        authViewModel.fullName = ""
        authViewModel.phoneNumber = ""
    }
}

// MARK: - Sign Up Form
private struct SignUpForm: View {
    @ObservedObject var authViewModel: AuthenticationViewModel
    @FocusState.Binding var focusedField: SignUpField?
    let onSignUp: () -> Void
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                VStack(spacing: 16) {
                    FormTextField(
                        title: "Full Name",
                        placeholder: "Enter your full name",
                        text: $authViewModel.fullName,
                        autocapitalization: .words,
                        errorMessage: fullNameErrorMessage
                    )
                    .focused($focusedField, equals: .fullName)
                    .onSubmit {
                        focusedField = .email
                    }
                    
                    FormTextField(
                        title: "Email",
                        placeholder: "<EMAIL>",
                        text: $authViewModel.email,
                        keyboardType: .emailAddress,
                        autocapitalization: .never,
                        errorMessage: emailErrorMessage
                    )
                    .focused($focusedField, equals: .email)
                    .onSubmit {
                        focusedField = .password
                    }
                    
                    FormTextField(
                        title: "Password",
                        placeholder: "At least \(AppConstants.Validation.minPasswordLength) characters",
                        text: $authViewModel.password,
                        isSecure: true,
                        errorMessage: passwordErrorMessage
                    )
                    .focused($focusedField, equals: .password)
                    .onSubmit {
                        focusedField = .phoneNumber
                    }
                    
                    FormTextField(
                        title: "Phone Number (Optional)",
                        placeholder: "+****************",
                        text: $authViewModel.phoneNumber,
                        keyboardType: .phonePad
                    )
                    .focused($focusedField, equals: .phoneNumber)
                    .onSubmit(onSignUp)
                }
                .standardPadding()
                
                PrimaryButton(
                    title: AppConstants.Text.Auth.createAccount,
                    isLoading: authViewModel.isSignUpLoading,
                    isEnabled: isFormValid
                ) {
                    onSignUp()
                }
                .standardPadding()
                .padding(.bottom, 32)
            }
        }
    }
    
    // MARK: - Computed Properties
    private var isFormValid: Bool {
        !authViewModel.fullName.isEmpty &&
        !authViewModel.email.isEmpty &&
        authViewModel.email.isValidEmail &&
        !authViewModel.password.isEmpty &&
        authViewModel.password.isValidPassword
    }
    
    private var fullNameErrorMessage: String? {
        guard !authViewModel.fullName.isEmpty else { return nil }
        return authViewModel.fullName.count >= 2 ? nil : "Name must be at least 2 characters"
    }
    
    private var emailErrorMessage: String? {
        guard !authViewModel.email.isEmpty else { return nil }
        return authViewModel.email.isValidEmail ? nil : "Please enter a valid email address"
    }
    
    private var passwordErrorMessage: String? {
        guard !authViewModel.password.isEmpty else { return nil }
        return authViewModel.password.isValidPassword ? nil : "Password must be at least \(AppConstants.Validation.minPasswordLength) characters"
    }
}

// MARK: - Sign Up Field
private enum SignUpField {
    case fullName, email, password, phoneNumber
}

#Preview {
    NavigationStack {
        SignUpView(authViewModel: AuthenticationViewModel())
    }
}
