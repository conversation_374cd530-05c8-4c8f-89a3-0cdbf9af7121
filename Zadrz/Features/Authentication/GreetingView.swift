//
//  GreetingView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI
import GoogleSignIn
import GoogleSignInSwift
import AuthenticationServices
import FirebaseAuth
import FirebaseDatabase

struct GreetingView: View {
    @ObservedObject var authViewModel: AuthenticationViewModel
    var onLogin: (() -> Void)? = nil

    var body: some View {
        NavigationStack {
            ZStack {
                MascotView()
                AuthenticationSheet(authViewModel: authViewModel, onLogin: onLogin)
            }
        }
    }
}

// MARK: - Mascot View
private struct MascotView: View {
    var body: some View {
        ZStack {
            // Concentric circles
            ForEach(0..<4, id: \.self) { index in
                Circle()
                    .stroke(
                        Color.white.opacity(0.15 - Double(index) * 0.03),
                        lineWidth: 2
                    )
                    .frame(width: 280 - CGFloat(index * 20))
            }
            
            // Main mascot
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [.yellow, .orange],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 120, height: 120)
                
                // Facial features
                VStack(spacing: 8) {
                    // Eyes
                    HStack(spacing: 16) {
                        ForEach(0..<2, id: \.self) { _ in
                            RoundedRectangle(cornerRadius: 10)
                                .fill(Color.black.opacity(0.8))
                                .frame(width: 8, height: 16)
                                .rotationEffect(.degrees(15))
                        }
                    }
                    .offset(y: -8)
                    
                    // Smile
                    Circle()
                        .trim(from: 0.2, to: 0.8)
                        .stroke(Color.black.opacity(0.8), lineWidth: 3)
                        .frame(width: 24, height: 24)
                        .rotationEffect(.degrees(180))
                        .offset(y: -4)
                }
                
                // Waving hands
                HStack(spacing: 80) {
                    Text("👋")
                        .font(.system(size: 24))
                        .offset(x: -20, y: 10)
                    
                    Text("👋")
                        .font(.system(size: 24))
                        .scaleEffect(x: -1, y: 1)
                        .offset(x: 20, y: 10)
                }
                
                // Heart
                HeartView()
                    .offset(y: 50)
            }
        }
        .offset(y: -100)
    }
}

// MARK: - Heart View
private struct HeartView: View {
    var body: some View {
        ZStack {
            // Glow effect
            Image(systemName: "heart.fill")
                .font(.title2)
                .foregroundStyle(
                    LinearGradient(
                        colors: [Color.pink.opacity(0.3), Color.purple.opacity(0.3)],
                        startPoint: .top,
                        endPoint: .bottom
                    )
                )
                .scaleEffect(1.8)
                .blur(radius: 8)
            
            // Main heart
            Image(systemName: "heart.fill")
                .font(.title2)
                .foregroundStyle(
                    LinearGradient(
                        colors: [.pink, .purple],
                        startPoint: .top,
                        endPoint: .bottom
                    )
                )
        }
    }
}

// MARK: - Authentication Sheet
private struct AuthenticationSheet: View {
    @ObservedObject var authViewModel: AuthenticationViewModel
    var onLogin: (() -> Void)? = nil
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        VStack(spacing: 20) {
            Spacer()
            
            Text("Chat with Legends. Learn. \nExplore. Discover.")
                .font(.largeTitle)
                .bold()
                .multilineTextAlignment(.center)
            
            Spacer()
            
            // Authentication buttons
            VStack(spacing: 16) {
                // Apple Sign In
                PrimaryButton(
                    title: "Sign in with Apple",
                    isLoading: authViewModel.isAppleSignInLoading
                ) {
                    Task {
                        await handleAppleSignIn()
                    }
                }
                .buttonStyle(
                    backgroundColor: .black,
                    foregroundColor: .white
                )
                
                // Google Sign In
                PrimaryButton(
                    title: "Sign in with Google",
                    isLoading: authViewModel.isGoogleSignInLoading
                ) {
                    Task {
                        await handleGoogleSignIn()
                    }
                }
                .buttonStyle(
                    backgroundColor: .white,
                    foregroundColor: .black
                )
                
                // Email Login
                NavigationLink(destination: LoginView(authViewModel: authViewModel, onAuthenticated: {
                    onLogin?()
                })) {
                    HStack(spacing: 8) {
                        Image(systemName: "envelope.fill")
                        Text("Login with Email")
                    }
                    .buttonStyle()
                }
                
                // Sign Up Link
                NavigationLink(destination: SignUpView(authViewModel: authViewModel)) {
                    Text(AppConstants.Text.Auth.dontHaveAccount + " Sign Up")
                        .font(.subheadline)
                        .foregroundColor(.accentColor)
                }
            }
            .standardPadding()
            .padding(.bottom, 32)
        }
        .background(.ultraThinMaterial)
        .sensoryFeedback(
            success: $authViewModel.successTrigger,
            error: $authViewModel.errorTrigger
        )
        .alert("Success", isPresented: $authViewModel.showSuccessAlert) {
            Button("OK") {
                onLogin?()
            }
        } message: {
            Text(authViewModel.successMessage ?? "")
        }
        .alert("Error", isPresented: $authViewModel.showErrorAlert) {
            Button("OK", role: .cancel) {}
        } message: {
            Text(authViewModel.errorMessage ?? "")
        }
    }
    
    // MARK: - Private Methods
    private func handleAppleSignIn() async {
        authViewModel.isAppleSignInLoading = true

        do {
            let request = ASAuthorizationAppleIDProvider().createRequest()
            request.requestedScopes = [.fullName, .email]

            let authorizationController = ASAuthorizationController(authorizationRequests: [request])

            let result = try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<ASAuthorization, Error>) in
                let delegate = AppleSignInDelegate(continuation: continuation)
                authorizationController.delegate = delegate
                authorizationController.presentationContextProvider = delegate
                authorizationController.performRequests()
            }

            guard let appleIDCredential = result.credential as? ASAuthorizationAppleIDCredential,
                  let identityToken = appleIDCredential.identityToken,
                  let idTokenString = String(data: identityToken, encoding: .utf8) else {
                authViewModel.showError("Failed to get Apple ID token")
                authViewModel.isAppleSignInLoading = false
                return
            }

            // Create Firebase credential for Apple Sign In
            let credential = OAuthProvider.appleCredential(
                withIDToken: idTokenString,
                rawNonce: nil,
                fullName: appleIDCredential.fullName
            )

            // Sign in to Firebase
            let authResult = try await Auth.auth().signIn(with: credential)

            // Create user model and save to database if new user
            if authResult.additionalUserInfo?.isNewUser == true {
                let fullName = [
                    appleIDCredential.fullName?.givenName,
                    appleIDCredential.fullName?.familyName
                ].compactMap { $0 }.joined(separator: " ")

                let userModel = UserModel(
                    id: authResult.user.uid,
                    fullName: fullName.isEmpty ? "Apple User" : fullName,
                    email: appleIDCredential.email ?? authResult.user.email ?? ""
                )

                try await AuthenticationService.shared.updateUserInDatabase([
                    "id": userModel.id,
                    "fullName": userModel.fullName,
                    "email": userModel.email,
                    "bio": userModel.bio,
                    "profileImageUrl": "",
                    "emailVerified": true,
                    "createdAt": ServerValue.timestamp()
                ])
            }

            authViewModel.showSuccess("Successfully signed in with Apple!")

        } catch {
            authViewModel.showError("Apple Sign In failed: \(error.localizedDescription)")
        }

        authViewModel.isAppleSignInLoading = false
    }
    
    private func handleGoogleSignIn() async {
        authViewModel.isGoogleSignInLoading = true

        do {
            // Get the client ID from GoogleService-Info.plist
            guard let clientID = getGoogleClientID() else {
                authViewModel.showError("Google Sign In configuration error")
                authViewModel.isGoogleSignInLoading = false
                return
            }

            // Configure Google Sign In
            let config = GIDConfiguration(clientID: clientID)
            GIDSignIn.sharedInstance.configuration = config

            // Get the presenting view controller
            guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                  let rootViewController = windowScene.windows.first?.rootViewController else {
                authViewModel.showError("Unable to present Google Sign In")
                authViewModel.isGoogleSignInLoading = false
                return
            }

            // Perform Google Sign In
            let result = try await GIDSignIn.sharedInstance.signIn(withPresenting: rootViewController)
            let user = result.user

            guard let idToken = user.idToken?.tokenString else {
                authViewModel.showError("Failed to get Google ID token")
                authViewModel.isGoogleSignInLoading = false
                return
            }

            // Create Firebase credential
            let credential = GoogleAuthProvider.credential(
                withIDToken: idToken,
                accessToken: user.accessToken.tokenString
            )

            // Sign in to Firebase
            let authResult = try await Auth.auth().signIn(with: credential)

            // Create user model and save to database if new user
            if authResult.additionalUserInfo?.isNewUser == true {
                let userModel = UserModel(
                    id: authResult.user.uid,
                    fullName: user.profile?.name ?? "",
                    email: user.profile?.email ?? ""
                )
                try await AuthenticationService.shared.updateUserInDatabase([
                    "id": userModel.id,
                    "fullName": userModel.fullName,
                    "email": userModel.email,
                    "bio": userModel.bio,
                    "profileImageUrl": user.profile?.imageURL(withDimension: 200)?.absoluteString ?? "",
                    "emailVerified": true,
                    "createdAt": ServerValue.timestamp()
                ])
            }

            authViewModel.showSuccess("Successfully signed in with Google!")

        } catch {
            authViewModel.showError("Google Sign In failed: \(error.localizedDescription)")
        }

        authViewModel.isGoogleSignInLoading = false
    }

    private func getGoogleClientID() -> String? {
        guard let path = Bundle.main.path(forResource: "GoogleService-Info", ofType: "plist"),
              let plist = NSDictionary(contentsOfFile: path),
              let clientID = plist["CLIENT_ID"] as? String else {
            return nil
        }
        return clientID
    }
}

// MARK: - Apple Sign In Delegate
private class AppleSignInDelegate: NSObject, ASAuthorizationControllerDelegate, ASAuthorizationControllerPresentationContextProviding {
    private let continuation: CheckedContinuation<ASAuthorization, Error>

    init(continuation: CheckedContinuation<ASAuthorization, Error>) {
        self.continuation = continuation
        super.init()
    }

    func authorizationController(controller: ASAuthorizationController, didCompleteWithAuthorization authorization: ASAuthorization) {
        continuation.resume(returning: authorization)
    }

    func authorizationController(controller: ASAuthorizationController, didCompleteWithError error: Error) {
        continuation.resume(throwing: error)
    }

    func presentationAnchor(for controller: ASAuthorizationController) -> ASPresentationAnchor {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first else {
            return UIWindow()
        }
        return window
    }
}

#Preview {
    NavigationStack {
        GreetingView(authViewModel: AuthenticationViewModel())
    }
}
