//
//  GreetingView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI
import GoogleSignIn
import GoogleSignInSwift

struct GreetingView: View {
    @ObservedObject var authViewModel: AuthenticationViewModel
    var onLogin: (() -> Void)? = nil

    var body: some View {
        NavigationStack {
            ZStack {
                MascotView()
                AuthenticationSheet(authViewModel: authViewModel, onLogin: onLogin)
            }
        }
    }
}

// MARK: - Mascot View
private struct MascotView: View {
    var body: some View {
        ZStack {
            // Concentric circles
            ForEach(0..<4, id: \.self) { index in
                Circle()
                    .stroke(
                        Color.white.opacity(0.15 - Double(index) * 0.03),
                        lineWidth: 2
                    )
                    .frame(width: 280 - CGFloat(index * 20))
            }
            
            // Main mascot
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [.yellow, .orange],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 120, height: 120)
                
                // Facial features
                VStack(spacing: 8) {
                    // Eyes
                    HStack(spacing: 16) {
                        ForEach(0..<2, id: \.self) { _ in
                            RoundedRectangle(cornerRadius: 10)
                                .fill(Color.black.opacity(0.8))
                                .frame(width: 8, height: 16)
                                .rotationEffect(.degrees(15))
                        }
                    }
                    .offset(y: -8)
                    
                    // Smile
                    Circle()
                        .trim(from: 0.2, to: 0.8)
                        .stroke(Color.black.opacity(0.8), lineWidth: 3)
                        .frame(width: 24, height: 24)
                        .rotationEffect(.degrees(180))
                        .offset(y: -4)
                }
                
                // Waving hands
                HStack(spacing: 80) {
                    Text("👋")
                        .font(.system(size: 24))
                        .offset(x: -20, y: 10)
                    
                    Text("👋")
                        .font(.system(size: 24))
                        .scaleEffect(x: -1, y: 1)
                        .offset(x: 20, y: 10)
                }
                
                // Heart
                HeartView()
                    .offset(y: 50)
            }
        }
        .offset(y: -100)
    }
}

// MARK: - Heart View
private struct HeartView: View {
    var body: some View {
        ZStack {
            // Glow effect
            Image(systemName: "heart.fill")
                .font(.title2)
                .foregroundStyle(
                    LinearGradient(
                        colors: [Color.pink.opacity(0.3), Color.purple.opacity(0.3)],
                        startPoint: .top,
                        endPoint: .bottom
                    )
                )
                .scaleEffect(1.8)
                .blur(radius: 8)
            
            // Main heart
            Image(systemName: "heart.fill")
                .font(.title2)
                .foregroundStyle(
                    LinearGradient(
                        colors: [.pink, .purple],
                        startPoint: .top,
                        endPoint: .bottom
                    )
                )
        }
    }
}

// MARK: - Authentication Sheet
private struct AuthenticationSheet: View {
    @ObservedObject var authViewModel: AuthenticationViewModel
    var onLogin: (() -> Void)? = nil
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        VStack(spacing: 20) {
            Spacer()
            
            Text("Chat with Legends. Learn. \nExplore. Discover.")
                .font(.largeTitle)
                .bold()
                .multilineTextAlignment(.center)
            
            Spacer()
            
            // Authentication buttons
            VStack(spacing: 16) {
                // Apple Sign In
                PrimaryButton(
                    title: "Sign in with Apple",
                    isLoading: authViewModel.isAppleSignInLoading
                ) {
                    Task {
                        await handleAppleSignIn()
                    }
                }
                .buttonStyle(
                    backgroundColor: .black,
                    foregroundColor: .white
                )
                
                // Google Sign In
                PrimaryButton(
                    title: "Sign in with Google",
                    isLoading: authViewModel.isGoogleSignInLoading
                ) {
                    Task {
                        await handleGoogleSignIn()
                    }
                }
                .buttonStyle(
                    backgroundColor: .white,
                    foregroundColor: .black
                )
                
                // Email Login
                NavigationLink(destination: LoginView(authViewModel: authViewModel, onAuthenticated: {
                    onLogin?()
                })) {
                    HStack(spacing: 8) {
                        Image(systemName: "envelope.fill")
                        Text("Login with Email")
                    }
                    .buttonStyle()
                }
                
                // Sign Up Link
                NavigationLink(destination: SignUpView(authViewModel: authViewModel)) {
                    Text(AppConstants.Text.Auth.dontHaveAccount + " Sign Up")
                        .font(.subheadline)
                        .foregroundColor(.accentColor)
                }
            }
            .standardPadding()
            .padding(.bottom, 32)
        }
        .background(.ultraThinMaterial)
        .sensoryFeedback(
            success: $authViewModel.successTrigger,
            error: $authViewModel.errorTrigger
        )
        .alert("Success", isPresented: $authViewModel.showSuccessAlert) {
            Button("OK") {
                onLogin?()
            }
        } message: {
            Text(authViewModel.successMessage ?? "")
        }
        .alert("Error", isPresented: $authViewModel.showErrorAlert) {
            Button("OK", role: .cancel) {}
        } message: {
            Text(authViewModel.errorMessage ?? "")
        }
    }
    
    // MARK: - Private Methods
    private func handleAppleSignIn() async {
        // TODO: Implement Apple Sign In
        authViewModel.showError("Apple Sign In not implemented yet")
    }
    
    private func handleGoogleSignIn() async {
        // TODO: Implement Google Sign In
        authViewModel.showError("Google Sign In not implemented yet")
    }
}

#Preview {
    NavigationStack {
        GreetingView(authViewModel: AuthenticationViewModel())
    }
}
