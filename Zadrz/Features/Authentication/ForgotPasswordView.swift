//
//  ForgotPasswordView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

struct ForgotPasswordView: View {
    @ObservedObject var authViewModel: AuthenticationViewModel
    @Environment(\.dismiss) private var dismiss
    @FocusState private var isEmailFocused: Bool
    
    var body: some View {
        VStack(spacing: 32) {
            headerSection
            
            emailSection
            
            Spacer()
            
            actionButtons
        }
        .padding()
        .navigationTitle("Reset Password")
        .navigationBarTitleDisplayMode(.large)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("Cancel") {
                    dismiss()
                }
            }
        }
        .onAppear {
            isEmailFocused = true
        }
        .sensoryFeedback(
            success: $authViewModel.successTrigger,
            error: $authViewModel.errorTrigger
        )
        .alert("Success", isPresented: $authViewModel.showSuccessAlert) {
            <PERSON><PERSON>("OK") {
                dismiss()
            }
        } message: {
            Text(authViewModel.successMessage ?? "")
        }
        .alert("Error", isPresented: $authViewModel.showErrorAlert) {
            But<PERSON>("OK", role: .cancel) {}
        } message: {
            Text(authViewModel.errorMessage ?? "")
        }
    }
    
    // MARK: - View Components
    private var headerSection: some View {
        VStack(spacing: 16) {
            Image(systemName: "envelope.circle.fill")
                .font(.system(size: 60))
                .foregroundStyle(.accentColor)
            
            VStack(spacing: 8) {
                Text("Forgot your password?")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text("Enter your email address and we'll send you a link to reset your password.")
                    .font(.body)
                    .foregroundStyle(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
    }
    
    private var emailSection: some View {
        FormTextField(
            title: "Email Address",
            placeholder: "<EMAIL>",
            text: $authViewModel.email,
            keyboardType: .emailAddress,
            autocapitalization: .never,
            errorMessage: emailErrorMessage
        )
        .focused($isEmailFocused)
        .onSubmit {
            handleResetPassword()
        }
    }
    
    private var actionButtons: some View {
        VStack(spacing: 16) {
            PrimaryButton(
                title: "Send Reset Link",
                isLoading: authViewModel.isLoading,
                isEnabled: isFormValid
            ) {
                handleResetPassword()
            }
            
            SecondaryButton(
                title: "Back to Login",
                isEnabled: !authViewModel.isLoading
            ) {
                dismiss()
            }
        }
    }
    
    // MARK: - Computed Properties
    private var isFormValid: Bool {
        !authViewModel.email.isEmpty && authViewModel.email.isValidEmail
    }
    
    private var emailErrorMessage: String? {
        guard !authViewModel.email.isEmpty else { return nil }
        return authViewModel.email.isValidEmail ? nil : "Please enter a valid email address"
    }
    
    // MARK: - Private Methods
    private func handleResetPassword() {
        isEmailFocused = false
        Task {
            await authViewModel.resetPassword()
        }
    }
}

#Preview {
    NavigationStack {
        ForgotPasswordView(authViewModel: AuthenticationViewModel())
    }
}
