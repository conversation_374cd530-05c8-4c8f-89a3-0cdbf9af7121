//
//  ConversationStyleView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

struct ConversationStyleView: View {
    @Environment(\.dismiss) private var dismiss
    
    private let conversationStyles = [
        "Deep discussions",
        "Quick Q&A",
        "Motivational Talks"
    ]
    
    var body: some View {
        SelectionView(
            title: "Choose Conversation Style",
            options: conversationStyles,
            allowMultipleSelection: false
        ) { selectedStyles in
            handleStyleSelection(selectedStyles)
        }
        .navigationTitle("Conversation Style")
    }
    
    private func handleStyleSelection(_ styles: [String]) {
        print("Selected conversation style: \(styles)")
        // Complete onboarding and navigate to main app
        completeOnboarding()
    }
    
    private func completeOnboarding() {
        // Update the last module to move to main app
//        UserDefaults.lastModuleId = "tabbar"
//        
//        // Post notification to update app root view
//        NotificationCenter.default.post(name: .onboardingCompleted, object: nil)
        
        // Dismiss the onboarding flow
        dismiss()
    }
}

#Preview {
    NavigationStack {
        ConversationStyleView()
    }
}
