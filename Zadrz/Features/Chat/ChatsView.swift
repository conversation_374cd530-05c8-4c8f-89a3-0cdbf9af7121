//
//  ChatsView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

struct ChatsView: View {
    @State private var selectedFilter = "Active Chats"
    @State private var searchText: String = ""
    
    var body: some View {
        NavigationStack {
            VStack(spacing: 0) {
                // Chat list
                ChatListView(selectedFilter: $selectedFilter)
            }
            .searchable(text: $searchText)
            .navigationTitle("Chats")
            .navigationDestination(for: ChatItem.self) { chat in
                ChatDetailView(chat: chat)
            }
        }
    }
}

// MARK: - Filter Buttons View
private struct FilterButtonsView: View {
    @Binding var selectedFilter: String
    private let filters = ["Active Chats", "Favorite", "Saved Chats"]
    
    var body: some View {
        // Filter buttons using CapsuleButton
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 8) {
                ForEach(filters, id: \.self) { filter in
                    CapsuleButton(
                        title: filter,
                        isSelected: selectedFilter == filter
                    ) {
                        selectedFilter = filter
                    }
                }
            }
            .padding(.horizontal)
            .padding(.vertical, 4)
        }
    }
}

// MARK: - Chat List View
private struct ChatListView: View {
    @State private var chats = ChatItem.sampleChats
    @Binding var selectedFilter: String
    
    var body: some View {
        List {
            FilterButtonsView(selectedFilter: $selectedFilter)
                .listRowSeparator(.hidden)
                .listRowInsets(EdgeInsets())
            
            ForEach(chats) { chat in
                NavigationLink(value: chat) {
                    ChatRowView(chat: chat)
                }
                .listRowBackground(chat.unreadCount > 0 ? Color.gray.opacity(0.1) : Color.clear)
            }
            .onDelete { indexSet in
                chats.remove(atOffsets: indexSet)
            }
        }
        .listStyle(.plain)
    }
}

// MARK: - Chat Row View
private struct ChatRowView: View {
    let chat: ChatItem
    
    var body: some View {
        HStack(spacing: 12) {
            // Avatar
            Circle()
                .fill(.secondary.opacity(0.4))
                .frame(width: 50, height: 50)
                .overlay(
                    Text("👩‍💼")
                        .font(.system(size: 25))
                )
            
            // Content
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(chat.name)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundStyle(.primary)
                    
                    Spacer()
                    
                    Text(chat.time)
                        .font(.system(size: 12))
                        .foregroundStyle(chat.isRead ? .secondary : Color.accentColor)
                    
                    if !chat.isRead {
                        Image(systemName: "checkmark")
                            .font(.system(size: 12))
                            .foregroundStyle(Color.accent)
                    }
                }
                
                HStack {
                    Text(chat.message)
                        .font(.system(size: 14))
                        .foregroundStyle(.secondary)
                        .lineLimit(2)
                    
                    Spacer()
                    
                    if chat.unreadCount > 0 {
                        Text("\(chat.unreadCount)")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(.black)
                            .frame(width: 20, height: 20)
                            .background(Circle().fill(Color.accent))
                    }
                }
            }
        }
        .padding(.vertical, 4)
    }
}

#Preview {
    NavigationStack {
        ChatsView()
    }
}
