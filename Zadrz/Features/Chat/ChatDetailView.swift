import SwiftUI

struct ChatDetailView: View {
    @State private var messageText = ""
    @State private var messages: [ChatMessage] = ChatMessage.sampleMessages
    
    let chat: ChatItem
    
    var body: some View {
        VStack(spacing: 0) {
            // Messages
            messagesScrollView
        }
        .ignoresSafeArea(.keyboard, edges: .bottom)
        .toolbarVisibility(.hidden, for: .tabBar)
        .toolbar {
            ToolbarItem(placement: .principal) {
                // Name and status
                VStack(spacing: 2) {
                    Text(chat.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundStyle(.primary)
                }
            }
            ToolbarItem(placement: .topBarTrailing) {
                Circle()
                    .fill(.secondary.opacity(0.3))
                    .frame(width: 40, height: 40)
                    .overlay(
                        Text("👨‍🔬")
                            .font(.system(size: 20))
                    )
            }
        }
        .safeAreaInset(edge: .bottom) {
            chatInputBar
        }
    }
    
    // MARK: - Messages
    private var messagesScrollView: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(messages) { message in
                    MessageBubbleView(message: message)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 20)
        }
        .defaultScrollAnchor(.bottom)
    }
    
    // MARK: - Input Bar
    private var chatInputBar: some View {
        HStack(spacing: 12) {
            // Plus button
            Button(action: {}) {
                Image(systemName: "plus.circle.fill")
                    .font(.title2)
                    .foregroundStyle(.tint)
            }
            
            // Text field
            HStack {
                TextField("Message", text: $messageText, axis: .vertical)
                    .textFieldStyle(.plain)
                
                Button(action: {}) {
                    Image(systemName: "paperclip")
                        .foregroundStyle(.tint)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 6)
            .background(.ultraThinMaterial, in: .capsule)
            
            // Send/Microphone button
            Button(action: {
                if messageText.isEmpty {
                    // Microphone action
                } else {
                    sendMessage()
                }
            }) {
                Image(systemName: messageText.isEmpty ? "mic.fill" : "arrow.up.circle.fill")
                    .font(.title2)
                    .foregroundStyle(.tint)
            }
        }
        .padding()
        .background(.ultraThinMaterial)
    }
    
    // MARK: - Actions
    private func sendMessage() {
        guard !messageText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }
        
        let newMessage = ChatMessage(
            text: messageText,
            isFromUser: true,
            timestamp: "23:30",
            messageType: .text
        )
        
        withAnimation {
            messages.append(newMessage)
        }
        messageText = ""
    }
}

// MARK: - Message Bubble View
private struct MessageBubbleView: View {
    let message: ChatMessage
    
    var body: some View {
        HStack {
            if message.isFromUser {
                Spacer()
                UserMessageBubble(message: message)
            } else {
                AIMessageBubble(message: message)
                Spacer()
            }
        }
    }
}

// MARK: - User Message Bubble
private struct UserMessageBubble: View {
    let message: ChatMessage
    
    var body: some View {
        VStack(alignment: .trailing, spacing: 4) {
            Text(message.text)
                .font(.body)
                .foregroundStyle(.white)
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(.tint, in: .buttonBorder)
            
            HStack(spacing: 8) {
                    Text(message.timestamp)
                        .font(.caption)
                        .foregroundStyle(.secondary)
                    
                    Image(systemName: "checkmark.circle.fill")
                        .font(.caption)
                        .foregroundStyle(.tint)
            }
        }
    }
}

// MARK: - AI Message Bubble
private struct AIMessageBubble: View {
    let message: ChatMessage
    
    var body: some View {
        HStack(alignment: .bottom, spacing: 8) {
            // Avatar for AI messages
            Circle()
                .fill(.orange)
                .frame(width: 24, height: 24)
                .overlay(
                    Text("🧠")
                        .font(.system(size: 12))
                )
            
            VStack(alignment: .leading, spacing: 4) {
                messageContent
                
                if message.messageType != .date {
                    Text(message.timestamp)
                        .font(.caption)
                        .foregroundStyle(.secondary)
                }
            }
        }
    }
    
    @ViewBuilder
    private var messageContent: some View {
        switch message.messageType {
        case .text:
            Text(message.text)
                .font(.body)
                .foregroundStyle(.primary)
                .padding(.horizontal, 16)
                .padding(.vertical, 16)
                .background(.ultraThinMaterial, in: .buttonBorder)
            
        case .file:
            FileMessageView(message: message)
            
        case .image:
            ImageMessageView(message: message)
            
        case .date:
            DateSeparatorView(message: message)
        }
    }
}

// MARK: - File Message View
private struct FileMessageView: View {
    let message: ChatMessage
    
    var body: some View {
        HStack {
            Image(systemName: "doc.fill")
                .font(.title2)
                .foregroundStyle(.tint)
            
            VStack(alignment: .leading) {
                Text("document_name.doc")
                    .font(.body)
                    .foregroundStyle(.primary)
                
                Text("48 kB")
                    .font(.caption)
                    .foregroundStyle(.secondary)
            }
            
            Spacer()
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(.ultraThinMaterial, in: .buttonBorder)
    }
}

// MARK: - Image Message View
private struct ImageMessageView: View {
    let message: ChatMessage
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // Image placeholder
            HStack(spacing: 4) {
                Rectangle()
                    .fill(.green.opacity(0.3))
                    .frame(width: 80, height: 80)
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                    .overlay(
                        Text("🌿")
                            .font(.title)
                    )
                
                Rectangle()
                    .fill(.blue.opacity(0.3))
                    .frame(width: 80, height: 80)
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                    .overlay(
                        Text("🌊")
                            .font(.title)
                    )
            }
            
            Text(message.text)
                .font(.system(size: 16))
                .foregroundStyle(.primary)
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(.ultraThinMaterial, in: .buttonBorder)
        }
    }
}

// MARK: - Date Separator
private struct DateSeparatorView: View {
    let message: ChatMessage
    
    var body: some View {
        HStack {
            Spacer()
            Text(message.text)
                .font(.caption)
                .foregroundStyle(.secondary)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(.ultraThinMaterial, in: .capsule)
            Spacer()
        }
    }
}

#Preview {
    NavigationStack {
        ChatDetailView(chat: ChatItem.sampleChats[0])
    }
}
