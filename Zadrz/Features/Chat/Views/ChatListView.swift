//
//  ChatListView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

struct ChatListView: View {
    let chats: [ChatItem]
    @Binding var selectedFilter: ChatFilter
    let onDeleteChat: (IndexSet) -> Void
    
    var body: some View {
        List {
            ChatFilterSection(selectedFilter: $selectedFilter)
                .listRowSeparator(.hidden)
                .listRowInsets(EdgeInsets())
            
            ForEach(chats) { chat in
                NavigationLink(value: chat) {
                    ChatRowView(chat: chat)
                        .equatable(chat.id) // Prevent unnecessary updates
                }
                .listRowOptimized()
                .listRowBackground(
                    chat.unreadCount > 0 ?
                    Color.accentColor.opacity(0.05) :
                    Color.clear
                )
                .swipeActions(edge: .trailing, allowsFullSwipe: false) {
                    But<PERSON>("Delete", role: .destructive) {
                        if let index = chats.firstIndex(where: { $0.id == chat.id }) {
                            onDeleteChat(IndexSet(integer: index))
                        }
                    }

                    <PERSON><PERSON>("Archive") {
                        // TODO: Implement archive functionality
                    }
                    .tint(.orange)
                }
            }
            .onDelete(perform: onDeleteChat)
        }
        .listStyle(.plain)
    }
}

// MARK: - Chat Filter Section
private struct ChatFilterSection: View {
    @Binding var selectedFilter: ChatFilter
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(ChatFilter.allCases, id: \.self) { filter in
                    FilterChip(
                        title: filter.title,
                        isSelected: selectedFilter == filter
                    ) {
                        withAnimation(.easeInOut(duration: AppConstants.UI.Animation.fastDuration)) {
                            selectedFilter = filter
                        }
                    }
                }
            }
            .padding(.horizontal, AppConstants.UI.padding)
            .padding(.vertical, AppConstants.UI.smallPadding)
        }
    }
}

// MARK: - Filter Chip
private struct FilterChip: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    Capsule()
                        .fill(isSelected ? .accentColor : .regularMaterial)
                )
                .foregroundStyle(isSelected ? .black : .primary)
        }
        .buttonStyle(.plain)
        .sensoryFeedback(.selection, trigger: isSelected)
    }
}

#Preview {
    NavigationStack {
        ChatListView(
            chats: ChatItem.sampleChats,
            selectedFilter: .constant(.active),
            onDeleteChat: { _ in }
        )
    }
}
