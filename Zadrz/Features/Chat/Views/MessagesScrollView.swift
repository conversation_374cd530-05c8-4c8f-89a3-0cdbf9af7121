//
//  MessagesScrollView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

struct MessagesScrollView: View {
    let messages: [ChatMessage]
    let isLoading: Bool
    
    var body: some View {
        ScrollViewReader { proxy in
            ScrollView {
                LazyVStack(spacing: 12) {
                    if isLoading {
                        loadingIndicator
                    }
                    
                    ForEach(messages) { message in
                        MessageBubbleView(message: message)
                            .id(message.id)
                    }
                }
                .padding(.horizontal, AppConstants.UI.padding)
                .padding(.vertical, AppConstants.UI.padding)
            }
            .defaultScrollAnchor(.bottom)
            .onChange(of: messages.count) { _, _ in
                if let lastMessage = messages.last {
                    withAnimation(.easeInOut(duration: AppConstants.UI.Animation.defaultDuration)) {
                        proxy.scrollTo(lastMessage.id, anchor: .bottom)
                    }
                }
            }
        }
    }
    
    // MARK: - Private Views
    private var loadingIndicator: some View {
        HStack {
            ProgressView()
                .scaleEffect(0.8)
            Text("Loading messages...")
                .font(.caption)
                .foregroundStyle(.secondary)
        }
        .padding()
    }
}

// MARK: - Message Bubble View
struct MessageBubbleView: View {
    let message: ChatMessage
    
    var body: some View {
        HStack {
            if message.isFromUser {
                Spacer()
                UserMessageBubble(message: message)
            } else {
                AIMessageBubble(message: message)
                Spacer()
            }
        }
    }
}

// MARK: - User Message Bubble
private struct UserMessageBubble: View {
    let message: ChatMessage
    
    var body: some View {
        VStack(alignment: .trailing, spacing: 4) {
            messageContent
            messageFooter
        }
        .frame(maxWidth: UIScreen.main.bounds.width * 0.75, alignment: .trailing)
    }
    
    private var messageContent: some View {
        Text(message.text)
            .font(.body)
            .foregroundStyle(.white)
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(.accent, in: .buttonBorder)
    }
    
    private var messageFooter: some View {
        HStack(spacing: 4) {
            Text(message.timestamp)
                .font(.caption2)
                .foregroundStyle(.secondary)
            
            Image(systemName: "checkmark.circle.fill")
                .font(.caption2)
                .foregroundStyle(.accent)
        }
    }
}

// MARK: - AI Message Bubble
private struct AIMessageBubble: View {
    let message: ChatMessage
    
    var body: some View {
        HStack(alignment: .bottom, spacing: 8) {
            aiAvatar
            
            VStack(alignment: .leading, spacing: 4) {
                messageContent
                
                if message.messageType != .date {
                    messageTimestamp
                }
            }
            .frame(maxWidth: UIScreen.main.bounds.width * 0.75, alignment: .leading)
        }
    }
    
    private var aiAvatar: some View {
        ZStack {
            Circle()
                .fill(.orange.gradient)
                .frame(width: 24, height: 24)
            
            Text("🧠")
                .font(.system(size: 12))
        }
    }
    
    @ViewBuilder
    private var messageContent: some View {
        switch message.messageType {
        case .text:
            Text(message.text)
                .font(.body)
                .foregroundStyle(.primary)
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(.regularMaterial, in: .buttonBorder)
            
        case .file:
            FileMessageView(message: message)
            
        case .image:
            ImageMessageView(message: message)
            
        case .date:
            DateSeparatorView(message: message)
        }
    }
    
    private var messageTimestamp: some View {
        Text(message.timestamp)
            .font(.caption2)
            .foregroundStyle(.secondary)
    }
}

#Preview {
    MessagesScrollView(
        messages: ChatMessage.sampleMessages,
        isLoading: false
    )
}
