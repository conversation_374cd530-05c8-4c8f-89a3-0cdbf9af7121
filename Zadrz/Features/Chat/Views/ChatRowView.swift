//
//  ChatRowView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

struct ChatRowView: View {
    let chat: ChatItem
    
    var body: some View {
        HStack(spacing: 12) {
            avatarView
            contentView
        }
        .padding(.vertical, 4)
    }
    
    // MARK: - Private Views
    private var avatarView: some View {
        ZStack {
            Circle()
                .fill(.regularMaterial)
                .frame(width: 50, height: 50)
            
            // TODO: Replace with actual user avatar when available
            Text(chat.name.prefix(1).uppercased())
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundStyle(.primary)
        }
    }
    
    private var contentView: some View {
        VStack(alignment: .leading, spacing: 4) {
            headerRow
            messageRow
        }
    }
    
    private var headerRow: some View {
        HStack {
            Text(chat.name)
                .font(.system(size: 16, weight: .semibold))
                .foregroundStyle(.primary)
                .lineLimit(1)
            
            Spacer()
            
            HStack(spacing: 4) {
                Text(chat.time)
                    .font(.caption)
                    .foregroundColor(chat.isRead ? .secondary : .accentColor)

                if !chat.isRead {
                    Image(systemName: AppConstants.SystemImages.checkmark)
                        .font(.caption2)
                        .foregroundColor(.accentColor)
                }
            }
        }
    }
    
    private var messageRow: some View {
        HStack {
            Text(chat.message)
                .font(.subheadline)
                .foregroundStyle(.secondary)
                .lineLimit(2)
                .multilineTextAlignment(.leading)
            
            Spacer()
            
            if chat.unreadCount > 0 {
                unreadBadge
            }
        }
    }
    
    private var unreadBadge: some View {
        Text("\(chat.unreadCount)")
            .font(.caption2)
            .fontWeight(.semibold)
            .foregroundColor(.black)
            .frame(minWidth: 20, minHeight: 20)
            .background(
                Circle()
                    .fill(Color.accentColor)
            )
    }
}

#Preview {
    List {
        ForEach(ChatItem.sampleChats.prefix(3), id: \.id) { chat in
            ChatRowView(chat: chat)
        }
    }
    .listStyle(.plain)
}
