//
//  ChatDetailView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

struct ChatDetailView: View {
    let chat: ChatItem
    @StateObject private var viewModel = ChatDetailViewModel()
    @FocusState private var isInputFocused: Bool
    
    var body: some View {
        VStack(spacing: 0) {
            MessagesScrollView(
                messages: viewModel.messages,
                isLoading: viewModel.isLoading
            )
        }
        .ignoresSafeArea(.keyboard, edges: .bottom)
        .toolbarVisibility(.hidden, for: .tabBar)
        .toolbar {
            ToolbarItem(placement: .principal) {
                ChatHeaderView(chat: chat)
            }
            
            ToolbarItem(placement: .topBarTrailing) {
                ChatAvatarView(chat: chat)
            }
        }
        .safeAreaInset(edge: .bottom) {
            ChatInputBar(
                messageText: $viewModel.messageText,
                isInputFocused: $isInputFocused,
                onSendMessage: viewModel.sendMessage,
                onAttachFile: viewModel.attachFile,
                onStartVoiceMessage: viewModel.startVoiceMessage
            )
        }
        .onAppear {
            viewModel.loadMessages(for: chat)
        }
    }
}

// MARK: - Chat Detail View Model
@MainActor
final class ChatDetailViewModel: ObservableObject {
    @Published var messages: [ChatMessage] = []
    @Published var messageText = ""
    @Published var isLoading = false
    @Published var isTyping = false
    
    func loadMessages(for chat: ChatItem) {
        // TODO: Load actual messages from backend
        messages = ChatMessage.sampleMessages
    }
    
    func sendMessage() {
        guard !messageText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }
        
        let newMessage = ChatMessage(
            text: messageText,
            isFromUser: true,
            timestamp: formatCurrentTime(),
            messageType: .text
        )
        
        withAnimation(.easeInOut(duration: AppConstants.UI.Animation.fastDuration)) {
            messages.append(newMessage)
        }
        
        messageText = ""
        
        // Simulate AI response
        simulateAIResponse()
    }
    
    func attachFile() {
        // TODO: Implement file attachment
        print("Attach file tapped")
    }
    
    func startVoiceMessage() {
        // TODO: Implement voice message
        print("Voice message tapped")
    }
    
    // MARK: - Private Methods
    private func simulateAIResponse() {
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            let responses = [
                "That's an interesting question! Let me think about that...",
                "I understand what you're asking. Here's my perspective...",
                "Great point! I'd like to add that...",
                "Thanks for sharing that with me. I think..."
            ]
            
            let randomResponse = responses.randomElement() ?? "I see what you mean."
            
            let aiMessage = ChatMessage(
                text: randomResponse,
                isFromUser: false,
                timestamp: self.formatCurrentTime(),
                messageType: .text
            )
            
            withAnimation(.easeInOut(duration: AppConstants.UI.Animation.fastDuration)) {
                self.messages.append(aiMessage)
            }
        }
    }
    
    private func formatCurrentTime() -> String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: Date())
    }
}

// MARK: - Chat Header View
private struct ChatHeaderView: View {
    let chat: ChatItem
    
    var body: some View {
        VStack(spacing: 2) {
            Text(chat.name)
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundStyle(.primary)
            
            Text("Online")
                .font(.caption)
                .foregroundStyle(.green)
        }
    }
}

// MARK: - Chat Avatar View
private struct ChatAvatarView: View {
    let chat: ChatItem
    
    var body: some View {
        ZStack {
            Circle()
                .fill(.regularMaterial)
                .frame(width: 40, height: 40)
            
            // TODO: Replace with actual avatar when available
            Text(chat.name.prefix(1).uppercased())
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundStyle(.primary)
        }
    }
}

#Preview {
    NavigationStack {
        ChatDetailView(chat: ChatItem.sampleChats[0])
    }
}
