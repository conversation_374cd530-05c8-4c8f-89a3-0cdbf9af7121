//
//  ChatsView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

struct ChatsView: View {
    @StateObject private var viewModel = ChatsViewModel()
    @Debounced private var searchText: String = ""

    var body: some View {
        NavigationStack {
            VStack(spacing: 0) {
                ChatListView(
                    chats: filteredChats,
                    selectedFilter: $viewModel.selectedFilter,
                    onDeleteChat: viewModel.deleteChat
                )
            }
            .searchable(text: $searchText, prompt: "Search chats...")
            .navigationTitle("Chats")
            .navigationDestination(for: ChatItem.self) { chat in
                LazyView(ChatDetailView(chat: chat))
            }
            .refreshable {
                await viewModel.refreshChats()
            }
        }
    }
    
    // MARK: - Computed Properties
    private var filteredChats: [ChatItem] {
        let filtered = viewModel.filteredChats(for: viewModel.selectedFilter)

        // Use debounced search text for better performance
        let debouncedSearchText = $searchText
        if debouncedSearchText.isEmpty {
            return filtered
        } else {
            return filtered.filter { chat in
                chat.name.localizedCaseInsensitiveContains(debouncedSearchText) ||
                chat.message.localizedCaseInsensitiveContains(debouncedSearchText)
            }
        }
    }
}

// MARK: - Chats View Model
@MainActor
final class ChatsViewModel: ObservableObject {
    @Published var chats: [ChatItem] = ChatItem.sampleChats
    @Published var selectedFilter: ChatFilter = .active
    @Published var isLoading = false
    
    func filteredChats(for filter: ChatFilter) -> [ChatItem] {
        switch filter {
        case .active:
            return chats.filter { !$0.isRead || $0.unreadCount > 0 }
        case .favorite:
            // TODO: Implement favorite filtering when favorite property is added
            return chats
        case .saved:
            // TODO: Implement saved filtering when saved property is added
            return chats
        }
    }
    
    func deleteChat(at indexSet: IndexSet) {
        chats.remove(atOffsets: indexSet)
    }
    
    func refreshChats() async {
        isLoading = true
        // TODO: Implement actual chat refresh from backend
        try? await Task.sleep(nanoseconds: 1_000_000_000) // 1 second delay
        isLoading = false
    }
}

// MARK: - Chat Filter
enum ChatFilter: String, CaseIterable {
    case active = "Active Chats"
    case favorite = "Favorite"
    case saved = "Saved Chats"
    
    var title: String {
        self.rawValue
    }
}

#Preview {
    ChatsView()
}
