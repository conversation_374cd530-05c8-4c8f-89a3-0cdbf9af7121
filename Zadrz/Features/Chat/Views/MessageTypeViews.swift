//
//  MessageTypeViews.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

// MARK: - File Message View
struct FileMessageView: View {
    let message: ChatMessage
    
    var body: some View {
        HStack(spacing: 12) {
            fileIcon
            fileInfo
            Spacer()
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(.regularMaterial, in: .buttonBorder)
        .onTapGesture {
            // TODO: Handle file tap
            print("File tapped: \(message.text)")
        }
    }
    
    private var fileIcon: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 8)
                .fill(.accentColor.opacity(0.2))
                .frame(width: 40, height: 40)
            
            Image(systemName: "doc.fill")
                .font(.title3)
                .foregroundStyle(.accentColor)
        }
    }
    
    private var fileInfo: some View {
        VStack(alignment: .leading, spacing: 2) {
            Text(message.text)
                .font(.body)
                .fontWeight(.medium)
                .foregroundStyle(.primary)
                .lineLimit(1)
            
            Text("48 KB • PDF")
                .font(.caption)
                .foregroundStyle(.secondary)
        }
    }
}

// MARK: - Image Message View
struct ImageMessageView: View {
    let message: ChatMessage
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            imageGrid
            
            if !message.text.isEmpty {
                Text(message.text)
                    .font(.body)
                    .foregroundStyle(.primary)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                    .background(.regularMaterial, in: .buttonBorder)
            }
        }
    }
    
    private var imageGrid: some View {
        HStack(spacing: 4) {
            imagePreview(color: .green, emoji: "🌿")
            imagePreview(color: .blue, emoji: "🌊")
        }
    }
    
    private func imagePreview(color: Color, emoji: String) -> some View {
        ZStack {
            RoundedRectangle(cornerRadius: 12)
                .fill(color.opacity(0.3))
                .frame(width: 80, height: 80)
            
            Text(emoji)
                .font(.title)
        }
        .onTapGesture {
            // TODO: Handle image tap
            print("Image tapped")
        }
    }
}

// MARK: - Date Separator View
struct DateSeparatorView: View {
    let message: ChatMessage
    
    var body: some View {
        HStack {
            Spacer()
            
            Text(message.text)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundStyle(.secondary)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(.regularMaterial, in: .capsule)
            
            Spacer()
        }
        .padding(.vertical, 8)
    }
}

// MARK: - Typing Indicator View
struct TypingIndicatorView: View {
    @State private var animationPhase = 0
    
    var body: some View {
        HStack(alignment: .bottom, spacing: 8) {
            // AI Avatar
            ZStack {
                Circle()
                    .fill(.orange.gradient)
                    .frame(width: 24, height: 24)
                
                Text("🧠")
                    .font(.system(size: 12))
            }
            
            // Typing animation
            HStack(spacing: 4) {
                ForEach(0..<3, id: \.self) { index in
                    Circle()
                        .fill(.secondary)
                        .frame(width: 6, height: 6)
                        .scaleEffect(animationPhase == index ? 1.2 : 0.8)
                        .animation(
                            .easeInOut(duration: 0.6)
                            .repeatForever()
                            .delay(Double(index) * 0.2),
                            value: animationPhase
                        )
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(.regularMaterial, in: .buttonBorder)
            
            Spacer()
        }
        .onAppear {
            animationPhase = 0
            withAnimation {
                animationPhase = 2
            }
        }
    }
}

#Preview {
    VStack(spacing: 20) {
        FileMessageView(
            message: ChatMessage(
                text: "document.pdf",
                isFromUser: false,
                timestamp: "12:30",
                messageType: .file
            )
        )
        
        ImageMessageView(
            message: ChatMessage(
                text: "Check out these beautiful nature photos!",
                isFromUser: false,
                timestamp: "12:31",
                messageType: .image
            )
        )
        
        DateSeparatorView(
            message: ChatMessage(
                text: "Today",
                isFromUser: false,
                timestamp: "",
                messageType: .date
            )
        )
        
        TypingIndicatorView()
    }
    .padding()
}
