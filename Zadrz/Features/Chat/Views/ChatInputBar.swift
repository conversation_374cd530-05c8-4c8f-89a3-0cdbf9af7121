//
//  ChatInputBar.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

struct ChatInputBar: View {
    @Binding var messageText: String
    @FocusState.Binding var isInputFocused: Bool
    let onSendMessage: () -> Void
    let onAttachFile: () -> Void
    let onStartVoiceMessage: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            attachmentButton
            messageInputField
            sendButton
        }
        .padding(AppConstants.UI.padding)
        .background(.ultraThinMaterial)
    }
    
    // MARK: - Private Views
    private var attachmentButton: some View {
        Button(action: onAttachFile) {
            Image(systemName: "plus.circle.fill")
                .font(.title2)
                .foregroundColor(.accentColor)
        }
        .buttonStyle(.plain)
    }
    
    private var messageInputField: some View {
        HStack(spacing: 8) {
            TextField("Message", text: $messageText, axis: .vertical)
                .textFieldStyle(.plain)
                .focused($isInputFocused)
                .lineLimit(1...4)
                .onSubmit {
                    if !messageText.isEmpty {
                        onSendMessage()
                    }
                }
            
            if !messageText.isEmpty {
                Button(action: clearMessage) {
                    Image(systemName: AppConstants.SystemImages.xmark)
                        .font(.caption)
                        .foregroundStyle(.secondary)
                }
                .buttonStyle(.plain)
            } else {
                Button(action: onAttachFile) {
                    Image(systemName: "paperclip")
                        .font(.body)
                        .foregroundColor(.accentColor)
                }
                .buttonStyle(.plain)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .background(.regularMaterial, in: .capsule)
    }
    
    private var sendButton: some View {
        Button(action: handleSendAction) {
            Image(systemName: messageText.isEmpty ? "mic.fill" : "arrow.up.circle.fill")
                .font(.title2)
                .foregroundColor(.accentColor)
        }
        .buttonStyle(.plain)
        .sensoryFeedback(.impact(flexibility: .soft), trigger: messageText.isEmpty)
    }
    
    // MARK: - Private Methods
    private func handleSendAction() {
        if messageText.isEmpty {
            onStartVoiceMessage()
        } else {
            onSendMessage()
        }
    }
    
    private func clearMessage() {
        messageText = ""
    }
}

#Preview {
    VStack {
        Spacer()
        ChatInputBar(
            messageText: .constant(""),
            isInputFocused: FocusState<Bool>().projectedValue,
            onSendMessage: {},
            onAttachFile: {},
            onStartVoiceMessage: {}
        )
    }
}
