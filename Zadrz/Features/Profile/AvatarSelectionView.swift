//
//  AvatarSelectionView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

struct AvatarSelectionView: View {
    @State private var searchText: String = ""

    private let rows = [
        GridItem(.adaptive(minimum: 100, maximum: .infinity), spacing: 16),
        GridItem(.adaptive(minimum: 100, maximum: .infinity), spacing: 16)
    ]
    
    var body: some View {
        NavigationStack {
            ScrollView {
                LazyVGrid(columns: rows, alignment: .center, spacing: 16) {
                    ForEach(0..<10, id: \.self) { index in
                        NavigationLink {
                            ChatDetailView(chat: ChatItem.sampleChats[0])
                        } label: {
                            AvatarCard(chatItem: ChatItem.sampleChats[0])
                        }
                    }
                }
                .navigationDestination(for: ChatItem.self, destination: { chatItem in
                    ChatDetailView(chat: chatItem)
                })
                .padding()
            }
            .navigationTitle("Avatars")
            .searchable(text: $searchText)
            .navigationDestination(for: ChatItem.self) { chat in
                ChatDeta<PERSON>Vie<PERSON>(chat: chat)
            }
        }
    }
}

struct AvatarCard: View {
    let chatItem: ChatItem
    
    var body: some View {
        VStack(spacing: 16.0) {
            Image(systemName: "person.crop.circle")
                .resizable()
                .frame(width: 80, height: 80)
                .clipShape(Circle())
            
            Text("Einstein")
                .fontDesign(.monospaced)
                .font(.title2)
                .bold()
            
            Text("Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor")
                .font(.footnote)
            
            NavigationLink(value: chatItem) {
                Text("Chat Now")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .frame(maxWidth: .infinity)
                    .frame(height: 52)
                    .foregroundStyle(.black)
                    .background(.accent)
                    .clipShape(.capsule)
            }
            
            Text("based on your \ninterest in Science")
                .font(.caption)
                .foregroundStyle(.tint)
        }
        .multilineTextAlignment(.center)
        .padding()
        .background(.ultraThinMaterial)
        .clipShape(RoundedRectangle(cornerRadius: 16))
    }
}

#Preview {
    NavigationStack {
        AvatarSelectionView()
    }
}
