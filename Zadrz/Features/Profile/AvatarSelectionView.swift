//
//  AvatarSelectionView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

struct AvatarSelectionView: View {
    @State private var searchText: String = ""
    @State private var debouncedSearchText: String = ""
    @State private var searchTask: Task<Void, Never>?
    @StateObject private var viewModel = AvatarSelectionViewModel()

    private let columns = [
        GridItem(.adaptive(minimum: 160, maximum: .infinity), spacing: 16)
    ]

    var body: some View {
        ScrollView {
            LazyVGrid(columns: columns, alignment: .center, spacing: 16) {
                ForEach(filteredAvatars) { avatar in
                    AvatarCard(avatar: avatar)
                        .equatable(avatar.id)
                }
            }
            .padding(AppConstants.UI.padding)
        }
        .navigationTitle("Choose Your Avatar")
        .searchable(text: $searchText, prompt: "Search avatars...")
        .refreshable {
            await viewModel.refreshAvatars()
        }
        .task {
            await viewModel.loadAvatars()
        }
        .onChange(of: searchText) { _, newValue in
            // Cancel previous search task
            searchTask?.cancel()

            // Debounce search text changes
            searchTask = Task {
                try? await Task.sleep(nanoseconds: 300_000_000) // 300ms delay
                if !Task.isCancelled && searchText == newValue {
                    debouncedSearchText = newValue
                }
            }
        }
    }

    // MARK: - Computed Properties
    private var filteredAvatars: [AvatarModel] {
        if debouncedSearchText.isEmpty {
            return viewModel.avatars
        } else {
            return viewModel.avatars.filter { avatar in
                avatar.name.localizedCaseInsensitiveContains(debouncedSearchText) ||
                avatar.description.localizedCaseInsensitiveContains(debouncedSearchText) ||
                avatar.category.localizedCaseInsensitiveContains(debouncedSearchText)
            }
        }
    }
}

// MARK: - Avatar Selection View Model
@MainActor
final class AvatarSelectionViewModel: ObservableObject {
    @Published var avatars: [AvatarModel] = []
    @Published var isLoading = false

    func loadAvatars() async {
        isLoading = true
        // TODO: Load from backend
        avatars = AvatarModel.sampleAvatars
        isLoading = false
    }

    func refreshAvatars() async {
        await loadAvatars()
    }
}

// MARK: - Avatar Model
struct AvatarModel: Identifiable {
    let id = UUID()
    let name: String
    let description: String
    let category: String
    let imageUrl: String?
    let emoji: String

    static let sampleAvatars = [
        AvatarModel(name: "Einstein", description: "Brilliant physicist who revolutionized our understanding of space and time", category: "Science", imageUrl: nil, emoji: "🧠"),
        AvatarModel(name: "Shakespeare", description: "Master playwright and poet of the English language", category: "Literature", imageUrl: nil, emoji: "📚"),
        AvatarModel(name: "Da Vinci", description: "Renaissance genius, artist, inventor, and scientist", category: "Art", imageUrl: nil, emoji: "🎨"),
        AvatarModel(name: "Curie", description: "Pioneering scientist who discovered radioactivity", category: "Science", imageUrl: nil, emoji: "⚛️"),
        AvatarModel(name: "Tesla", description: "Visionary inventor and electrical engineer", category: "Technology", imageUrl: nil, emoji: "⚡"),
        AvatarModel(name: "Gandhi", description: "Leader of peaceful resistance and social change", category: "Philosophy", imageUrl: nil, emoji: "🕊️")
    ]
}

// MARK: - Avatar Card
struct AvatarCard: View {
    let avatar: AvatarModel

    var body: some View {
        VStack(spacing: 16) {
            avatarImage
            avatarInfo
            chatButton
            categoryTag
        }
        .multilineTextAlignment(.center)
        .padding(AppConstants.UI.padding)
        .cardStyle()
    }

    private var avatarImage: some View {
        ZStack {
            Circle()
                .fill(.regularMaterial)
                .frame(width: 80, height: 80)

            Text(avatar.emoji)
                .font(.system(size: 40))
        }
    }

    private var avatarInfo: some View {
        VStack(spacing: 8) {
            Text(avatar.name)
                .font(.title2)
                .fontWeight(.bold)
                .fontDesign(.rounded)

            Text(avatar.description)
                .font(.footnote)
                .foregroundStyle(.secondary)
                .lineLimit(3)
        }
    }

    private var chatButton: some View {
        NavigationLink(value: createChatItem()) {
            Text("Chat Now")
                .buttonStyle()
        }
    }

    private var categoryTag: some View {
        Text("Expert in \(avatar.category)")
            .font(.caption)
            .foregroundColor(.accentColor)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(Color.accentColor.opacity(0.1), in: .capsule)
    }

    private func createChatItem() -> ChatItem {
        ChatItem(
            name: avatar.name,
            message: "Hello! I'm \(avatar.name). How can I help you today?",
            time: "now",
            unreadCount: 0,
            isRead: true
        )
    }
}

#Preview {
    NavigationStack {
        AvatarSelectionView()
    }
}
