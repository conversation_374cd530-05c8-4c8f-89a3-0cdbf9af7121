//
//  DeleteAccountView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

struct DeleteAccountView: View {
    @ObservedObject var authViewModel: AuthenticationViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var showConfirmAlert = false
    @FocusState private var isPasswordFocused: Bool
    
    var body: some View {
        VStack(spacing: 24) {
            headerSection
            warningSection
            passwordSection
            Spacer()
            deleteButton
        }
        .padding()
        .navigationTitle(AppConstants.Text.Profile.deleteAccount)
        .onAppear {
            isPasswordFocused = true
        }
        .sensoryFeedback(
            success: $authViewModel.successTrigger,
            error: $authViewModel.errorTrigger
        )
        .alert("Account Deleted", isPresented: $authViewModel.showSuccessAlert) {
            Button("OK") {
                UINotificationFeedbackGenerator().notificationOccurred(.success)
                dismiss()
            }
        } message: {
            Text(authViewModel.successMessage ?? "")
        }
        .alert("Error", isPresented: $authViewModel.showErrorAlert) {
            But<PERSON>("OK", role: .cancel) {
                UINotificationFeedbackGenerator().notificationOccurred(.error)
            }
        } message: {
            Text(authViewModel.errorMessage ?? "")
        }
    }
    
    // MARK: - Private Views
    private var headerSection: some View {
        VStack(spacing: 16) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 60))
                .foregroundStyle(.red)
            
            Text("Delete Account")
                .font(.title2)
                .fontWeight(.semibold)
        }
    }
    
    private var warningSection: some View {
        VStack(spacing: 12) {
            Text("This action cannot be undone")
                .font(.headline)
                .foregroundStyle(.red)
            
            Text("Deleting your account will permanently remove all your data, including chats, preferences, and profile information.")
                .font(.body)
                .foregroundStyle(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding()
        .cardStyle()
    }
    
    private var passwordSection: some View {
        VStack(spacing: 8) {
            FormTextField(
                title: "Current password",
                placeholder: "Enter your current password",
                text: $authViewModel.currentPassword,
                isSecure: true
            )
            .focused($isPasswordFocused)
            .onSubmit {
                if isFormValid {
                    showConfirmAlert = true
                }
            }
        }
    }
    
    private var deleteButton: some View {
        VStack(spacing: 16) {
            PrimaryButton(
                title: "Delete My Account",
                isLoading: authViewModel.isLoading,
                isEnabled: isFormValid
            ) {
                showConfirmAlert = true
            }
            .buttonStyle(
//                backgroundColor: .red,
//                foregroundColor: .white
                isEnabled: isFormValid && !authViewModel.isLoading
            )
        }
        .alert("Are you absolutely sure?", isPresented: $showConfirmAlert) {
            Button("Cancel", role: .cancel) {
                UIImpactFeedbackGenerator(style: .light).impactOccurred()
            }
            Button("Delete Account", role: .destructive) {
                UINotificationFeedbackGenerator().notificationOccurred(.warning)
                handleDeleteAccount()
            }
        } message: {
            Text("This action will permanently remove your account and all associated data. This cannot be undone.")
        }
    }
    
    // MARK: - Computed Properties
    private var isFormValid: Bool {
        !authViewModel.currentPassword.isEmpty
    }
    
    // MARK: - Private Methods
    private func handleDeleteAccount() {
        isPasswordFocused = false
        Task {
            do {
                try await AuthenticationService.shared.deleteAccount(password: authViewModel.currentPassword)
                authViewModel.showSuccess("Account deleted successfully.")
                // Navigation will be handled by auth state change
            } catch {
                authViewModel.showError("Failed to delete account: \(error.localizedDescription)")
            }
        }
    }
}

#Preview {
    NavigationStack {
        DeleteAccountView(authViewModel: AuthenticationViewModel())
    }
}
