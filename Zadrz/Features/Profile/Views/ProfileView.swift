//
//  ProfileView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

struct ProfileView: View {
    @ObservedObject var authViewModel: AuthenticationViewModel
    @State private var showLogoutAlert = false
    @State private var isRefreshing = false

    var body: some View {
        NavigationStack {
            List {
                ProfileHeaderSection(
                    user: authViewModel.currentUser,
                    authViewModel: authViewModel
                )
                .listSectionSeparator(.hidden)
                
                ProfileOptionsSection(
                    showLogoutAlert: $showLogoutAlert,
                    onLogout: handleLogout
                )
            }
            .listStyle(.plain)
            .navigationTitle(AppConstants.Text.Profile.profile)
            .refreshable {
                await handleRefresh()
            }
        }
        .task {
            await loadUserDataIfNeeded()
        }
    }
    
    // MARK: - Private Methods
    private func loadUserDataIfNeeded() async {
        if authViewModel.currentUser == nil {
            await authViewModel.loadCurrentUser()
        }
    }
    
    private func handleLogout() {
        Task { 
            await authViewModel.logout() 
        }
    }
    
    private func handleRefresh() async {
        isRefreshing = true
        await authViewModel.loadCurrentUser()
        isRefreshing = false
    }
}

#Preview {
    NavigationStack {
        ProfileView(authViewModel: AuthenticationViewModel())
    }
}
