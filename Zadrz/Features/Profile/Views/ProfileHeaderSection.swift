//
//  ProfileHeaderSection.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

struct ProfileHeaderSection: View {
    let user: UserModel?
    @ObservedObject var authViewModel: AuthenticationViewModel
    
    var body: some View {
        Section {
            VStack(spacing: 12) {
                profileImage
                userNameText
                userEmailText
                emailVerificationStatus
            }
            .padding(.vertical, 20)
            .frame(maxWidth: .infinity)
        }
    }
    
    // MARK: - Private Views
    private var profileImage: some View {
        AsyncImage(url: URL(string: user?.profileImageUrl ?? "")) { image in
            image
                .resizable()
                .aspectRatio(contentMode: .fill)
        } placeholder: {
            ZStack {
                Circle()
                    .fill(.regularMaterial)
                
                if let user = user {
                    Text(user.initials)
                        .font(.title)
                        .fontWeight(.semibold)
                        .foregroundStyle(.primary)
                } else {
                    Image(systemName: AppConstants.SystemImages.personFill)
                        .font(.title)
                        .foregroundStyle(.secondary)
                }
            }
        }
        .frame(width: 80, height: 80)
        .clipShape(Circle())
    }
    
    private var userNameText: some View {
        Text(user?.displayName ?? "Unknown User")
            .font(.title2)
            .fontWeight(.semibold)
    }
    
    @ViewBuilder
    private var userEmailText: some View {
        if let user = user, !user.fullName.isEmpty {
            Text(user.email)
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
    }
    
    @ViewBuilder
    private var emailVerificationStatus: some View {
        if let user = user, !user.emailVerified {
            Button(action: handleResendVerification) {
                HStack {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .foregroundColor(.orange)
                    Text("Email not verified - Tap to resend")
                        .font(.caption)
                        .foregroundColor(.orange)
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 4)
                .background(Color.orange.opacity(0.1))
                .clipShape(Capsule())
            }
            .buttonStyle(.plain)
            .sensoryFeedback(
                success: $authViewModel.successTrigger,
                error: $authViewModel.errorTrigger
            )
            .alert("Verification Email Sent", isPresented: $authViewModel.showSuccessAlert) {
                Button("OK", role: .cancel) {}
            } message: {
                Text(authViewModel.successMessage ?? "")
            }
            .alert("Error", isPresented: $authViewModel.showErrorAlert) {
                Button("OK", role: .cancel) {}
            } message: {
                Text(authViewModel.errorMessage ?? "")
            }
        }
    }
    
    // MARK: - Private Methods
    private func handleResendVerification() {
        Task {
            do {
                try await AuthenticationService.shared.resendEmailVerification()
                authViewModel.showSuccess("Verification email sent successfully!")
            } catch {
                authViewModel.showError("Failed to send verification email: \(error.localizedDescription)")
            }
        }
    }
}

#Preview {
    List {
        ProfileHeaderSection(
            user: UserModel.sample,
            authViewModel: AuthenticationViewModel()
        )
    }
    .listStyle(.plain)
}
