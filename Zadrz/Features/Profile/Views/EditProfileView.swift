//
//  EditProfileView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

// MARK: - Account Management Destination
enum AccountManagementDestination: String, CaseIterable {
    case updateEmail = "Update email"
    case updatePassword = "Update password"
    case deleteAccount = "Delete account"
    
    var icon: String {
        switch self {
        case .updateEmail: return "envelope"
        case .updatePassword: return "lock"
        case .deleteAccount: return "trash"
        }
    }
    
    var isDestructive: Bool {
        self == .deleteAccount
    }
}

// MARK: - Edit Profile View
struct EditProfileView: View {
    @StateObject private var authViewModel = AuthenticationViewModel()
    
    var body: some View {
        List {
            Section(header: Text("Account Management")) {
                ForEach(AccountManagementDestination.allCases, id: \.self) { destination in
                    NavigationLink(destination: destinationView(for: destination)) {
                        ProfileItemView(
                            labelText: destination.rawValue,
                            labelImage: destination.icon
                        )
                    }
                    .padding(.vertical, 4)
                    .foregroundColor(destination.isDestructive ? .red : .primary)
                }
            }
        }
        .navigationTitle(AppConstants.Text.Profile.editProfile)
    }
    
    // MARK: - Private Methods
    @ViewBuilder
    private func destinationView(for destination: AccountManagementDestination) -> some View {
        switch destination {
        case .updateEmail:
            UpdateEmailView(authViewModel: authViewModel)
        case .updatePassword:
            UpdatePasswordView(authViewModel: authViewModel)
        case .deleteAccount:
            DeleteAccountView(authViewModel: authViewModel)
        }
    }
}

// MARK: - Update Email View
struct UpdateEmailView: View {
    @ObservedObject var authViewModel: AuthenticationViewModel
    @Environment(\.dismiss) private var dismiss
    @FocusState private var focusedField: EmailUpdateField?
    
    var body: some View {
        VStack(spacing: 24) {
            headerSection
            formSection
            Spacer()
            updateButton
        }
        .padding()
        .navigationTitle("Update Email")
        .navigationBarTitleDisplayMode(.large)
        .onAppear {
            focusedField = .currentPassword
        }
        .sensoryFeedback(
            success: $authViewModel.successTrigger,
            error: $authViewModel.errorTrigger
        )
        .alert("Success", isPresented: $authViewModel.showSuccessAlert) {
            Button("OK") {
                dismiss()
            }
        } message: {
            Text(authViewModel.successMessage ?? "")
        }
        .alert("Error", isPresented: $authViewModel.showErrorAlert) {
            Button("OK", role: .cancel) {}
        } message: {
            Text(authViewModel.errorMessage ?? "")
        }
    }
    
    // MARK: - Private Views
    private var headerSection: some View {
        VStack(spacing: 8) {
            Text("Update your email address")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("You'll need to verify your new email address")
                .font(.body)
                .foregroundStyle(.secondary)
                .multilineTextAlignment(.center)
        }
    }
    
    private var formSection: some View {
        VStack(spacing: 16) {
            FormTextField(
                title: "Current Password",
                placeholder: "Enter your current password",
                text: $authViewModel.currentPassword,
                isSecure: true
            )
            .focused($focusedField, equals: .currentPassword)
            .onSubmit {
                focusedField = .newEmail
            }
            
            FormTextField(
                title: "New Email",
                placeholder: "Enter your new email",
                text: $authViewModel.newEmail,
                keyboardType: .emailAddress,
                autocapitalization: .never,
                errorMessage: emailErrorMessage
            )
            .focused($focusedField, equals: .newEmail)
            .onSubmit {
                handleUpdateEmail()
            }
        }
    }
    
    private var updateButton: some View {
        PrimaryButton(
            title: "Update Email",
            isLoading: authViewModel.isLoading,
            isEnabled: isFormValid
        ) {
            handleUpdateEmail()
        }
    }
    
    // MARK: - Computed Properties
    private var isFormValid: Bool {
        !authViewModel.currentPassword.isEmpty &&
        !authViewModel.newEmail.isEmpty &&
        authViewModel.newEmail.isValidEmail
    }
    
    private var emailErrorMessage: String? {
        guard !authViewModel.newEmail.isEmpty else { return nil }
        return authViewModel.newEmail.isValidEmail ? nil : "Please enter a valid email address"
    }
    
    // MARK: - Private Methods
    private func handleUpdateEmail() {
        focusedField = nil
        Task {
            do {
                try await AuthenticationService.shared.updateEmail(
                    currentPassword: authViewModel.currentPassword,
                    newEmail: authViewModel.newEmail
                )
                authViewModel.showSuccess("Email update initiated. Please check your new email for verification.")
                authViewModel.currentPassword = ""
                authViewModel.newEmail = ""
            } catch {
                authViewModel.showError("Failed to update email: \(error.localizedDescription)")
            }
        }
    }
}

// MARK: - Email Update Field
private enum EmailUpdateField {
    case currentPassword, newEmail
}

#Preview {
    NavigationStack {
        EditProfileView()
    }
}
