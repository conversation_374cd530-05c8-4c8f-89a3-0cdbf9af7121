//
//  ProfileOptionsSection.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

// MARK: - Profile Destination
enum ProfileDestination: String, Hashable, CaseIterable {
    case editProfile = "Edit profile"
    case savedChats = "Saved chats"
    case referFriend = "Refer a friend"
    case notification = "Notification"
    case suggestLegend = "Suggest a legend"
    
    var icon: String {
        switch self {
        case .editProfile: return AppConstants.SystemImages.edit
        case .savedChats: return "message.fill"
        case .referFriend: return "arrowshape.turn.up.right.fill"
        case .notification: return "bell.badge.fill"
        case .suggestLegend: return "star.hexagon.fill"
        }
    }
}

// MARK: - Profile Options Section
struct ProfileOptionsSection: View {
    @Binding var showLogoutAlert: Bool
    let onLogout: () -> Void
    
    var body: some View {
        Section {
            // Edit Profile
            NavigationLink(destination: EditProfileView()) {
                ProfileItemView(
                    labelText: ProfileDestination.editProfile.rawValue,
                    labelImage: ProfileDestination.editProfile.icon
                )
            }
            .padding()
            
            // Other Options
            ForEach(otherDestinations, id: \.self) { destination in
                NavigationLink(destination: ProfileDetailView(destination: destination)) {
                    ProfileItemView(
                        labelText: destination.rawValue,
                        labelImage: destination.icon
                    )
                }
                .padding()
            }
            
            // Logout Button
            Button(action: { showLogoutAlert = true }) {
                ProfileItemView(
                    labelText: AppConstants.Text.Profile.logout,
                    labelImage: AppConstants.SystemImages.logout
                )
                .foregroundColor(.red)
            }
            .sensoryFeedback(.warning, trigger: showLogoutAlert)
            .padding()
            .alert("Are you sure you want to logout?", isPresented: $showLogoutAlert) {
                Button("Cancel", role: .cancel, action: {})
                Button(AppConstants.Text.Profile.logout, role: .destructive) {
                    UINotificationFeedbackGenerator().notificationOccurred(.success)
                    onLogout()
                }
            }
        }
    }
    
    // MARK: - Private Properties
    private var otherDestinations: [ProfileDestination] {
        [.savedChats, .referFriend, .notification, .suggestLegend]
    }
}

// MARK: - Profile Item View
struct ProfileItemView: View {
    let labelText: String
    let labelImage: String
    
    var body: some View {
        Label(labelText, systemImage: labelImage)
            .font(.body)
    }
}

// MARK: - Profile Detail View
struct ProfileDetailView: View {
    let destination: ProfileDestination
    
    var body: some View {
        VStack {
            Text(destination.rawValue)
                .font(.title)
                .padding()
            
            Text("This feature is coming soon!")
                .font(.body)
                .foregroundStyle(.secondary)
                .padding()
            
            Spacer()
        }
        .navigationTitle(destination.rawValue)
        .navigationBarTitleDisplayMode(.large)
    }
}

#Preview {
    NavigationStack {
        List {
            ProfileOptionsSection(
                showLogoutAlert: .constant(false),
                onLogout: {}
            )
        }
        .listStyle(.plain)
    }
}
