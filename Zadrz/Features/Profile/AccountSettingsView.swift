import SwiftUI

struct AccountSettingsView: View {
    @StateObject private var authVM = AuthScreenViewModel()
    @State private var showUpdateEmail = false
    @State private var showUpdatePassword = false
    @State private var showDeleteAccount = false
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
//        List {
//            Section("Account") {
//                Button {
//                    showUpdateEmail = true
//                } label: {
//                    HStack {
//                        Label("Change email", systemImage: "envelope")
//                        Spacer()
//                        Image(systemName: "chevron.right")
//                            .font(.caption)
//                            .foregroundColor(.secondary)
//                    }
//                }
//                .foregroundColor(.primary)
//                .padding(.vertical, 4)
//                
//                Button {
//                    showUpdatePassword = true
//                } label: {
//                    HStack {
//                        Label("Change password", systemImage: "lock")
//                        Spacer()
//                        Image(systemName: "chevron.right")
//                            .font(.caption)
//                            .foregroundColor(.secondary)
//                    }
//                }
//                .foregroundColor(.primary)
//                .padding(.vertical, 4)
//            }
//            
//            Section {
//                Button(role: .destructive) {
//                    showDeleteAccount = true
//                } label: {
//                    Label("Delete account", systemImage: "person.crop.circle.badge.xmark")
//                        .foregroundColor(.red)
//                }
//                .padding(.vertical, 4)
//            }
//        }
//        .navigationTitle("Account settings")
//        .sheet(isPresented: $showUpdateEmail) {
//            NavigationStack {
//                UpdateEmailView(authVM: authVM)
//            }
//        }
//        .sheet(isPresented: $showUpdatePassword) {
//            NavigationStack {
//                UpdatePasswordView(authVM: authVM)
//            }
//        }
//        .alert("Delete account", isPresented: $showDeleteAccount) {
//            Button("Cancel", role: .cancel) {
//                UIImpactFeedbackGenerator(style: .light).impactOccurred()
//            }
//            Button("Delete", role: .destructive) {
//                UINotificationFeedbackGenerator().notificationOccurred(.warning)
//                // Implement account deletion flow here
//                // This should use a more comprehensive view with password confirmation
//            }
//        } message: {
//            Text("Are you sure you want to delete your account? This action cannot be undone.")
//        }
    }
}

struct UpdateEmailView: View {
    @ObservedObject var authVM: AuthScreenViewModel
    @Environment(\.dismiss) private var dismiss
    @FocusState private var focusedField: EmailUpdateField?
    
    enum EmailUpdateField {
        case currentPassword, newEmail
    }
    
    var body: some View {
        VStack(spacing: 24) {
            FormFieldsSection(
                authVM: authVM,
                focusedField: $focusedField,
                onSubmit: updateEmail
            )
            
            UpdateButton(
                title: "Update email",
                isLoading: authVM.isLoading,
                isDisabled: authVM.currentPassword.isEmpty || authVM.newEmail.isEmpty,
                action: updateEmail
            )
            
            Spacer()
        }
        .padding()
        .navigationTitle("Change email")
        .sensoryFeedback(.success, trigger: authVM.successTrigger)
        .sensoryFeedback(.error, trigger: authVM.errorTrigger)
        .alert("Success", isPresented: $authVM.showSuccessAlert) {
            Button("Ok") {
                UINotificationFeedbackGenerator().notificationOccurred(.success)
                dismiss()
            }
        } message: {
            Text(authVM.successMessage)
        }
        .alert("Error", isPresented: $authVM.errorState.showError) {
            Button("Ok", role: .cancel) {
                UINotificationFeedbackGenerator().notificationOccurred(.error)
                authVM.errorState = (false, "")
            }
        } message: {
            Text(authVM.errorState.errorMessage)
        }
        .task {
            focusedField = .currentPassword
        }
    }
    
    private func updateEmail() {
        Task { await authVM.handleUpdateEmail() }
    }
}

struct UpdatePasswordView: View {
    @ObservedObject var authVM: AuthScreenViewModel
    @Environment(\.dismiss) private var dismiss
    @FocusState private var focusedField: PasswordUpdateField?
    
    enum PasswordUpdateField {
        case currentPassword, newPassword, confirmPassword
    }
    
    var body: some View {
        VStack(spacing: 24) {
            PasswordFormSection(
                authVM: authVM,
                focusedField: $focusedField,
                onSubmit: updatePassword
            )
            
            UpdateButton(
                title: "Update password",
                isLoading: authVM.isLoading,
                isDisabled: isFormInvalid,
                action: updatePassword
            )
            
            Spacer()
        }
        .padding()
        .navigationTitle("Change password")
        .sensoryFeedback(.success, trigger: authVM.successTrigger)
        .sensoryFeedback(.error, trigger: authVM.errorTrigger)
        .alert("Success", isPresented: $authVM.showSuccessAlert) {
            Button("Ok") {
                UINotificationFeedbackGenerator().notificationOccurred(.success)
                dismiss()
            }
        } message: {
            Text(authVM.successMessage)
        }
        .alert("Error", isPresented: $authVM.errorState.showError) {
            Button("Ok", role: .cancel) {
                UINotificationFeedbackGenerator().notificationOccurred(.error)
                authVM.errorState = (false, "")
            }
        } message: {
            Text(authVM.errorState.errorMessage)
        }
        .task {
            focusedField = .currentPassword
        }
    }
    
    private var isFormInvalid: Bool {
        authVM.currentPassword.isEmpty ||
        authVM.newPassword.isEmpty ||
        authVM.passwordConfirmation.isEmpty
    }
    
    private func updatePassword() {
        Task { await authVM.handleUpdatePassword() }
    }
}

// MARK: - Reusable Form Components
private struct FormFieldsSection: View {
    @ObservedObject var authVM: AuthScreenViewModel
    @FocusState.Binding var focusedField: UpdateEmailView.EmailUpdateField?
    let onSubmit: () -> Void
    
    var body: some View {
        VStack(spacing: 16) {
            AppTextField(
                title: "Current password",
                placeholder: "Enter your current password",
                text: $authVM.currentPassword,
                isSecure: true,
                onSubmit: { focusedField = .newEmail }
            )
            .focused($focusedField, equals: .currentPassword)
            
            AppTextField(
                title: "New email",
                placeholder: "Enter new email",
                text: $authVM.newEmail,
                keyboardType: .emailAddress,
                autocapitalization: .never,
                onSubmit: onSubmit
            )
            .focused($focusedField, equals: .newEmail)
        }
    }
}

private struct PasswordFormSection: View {
    @ObservedObject var authVM: AuthScreenViewModel
    @FocusState.Binding var focusedField: UpdatePasswordView.PasswordUpdateField?
    let onSubmit: () -> Void
    
    var body: some View {
        VStack(spacing: 16) {
            AppTextField(
                title: "Current password",
                placeholder: "Enter your current password",
                text: $authVM.currentPassword,
                isSecure: true,
                onSubmit: { focusedField = .newPassword }
            )
            .focused($focusedField, equals: .currentPassword)
            
            AppTextField(
                title: "New password",
                placeholder: "Enter new password",
                text: $authVM.newPassword,
                isSecure: true,
                onSubmit: { focusedField = .confirmPassword }
            )
            .focused($focusedField, equals: .newPassword)
            
            AppTextField(
                title: "Confirm new password",
                placeholder: "Confirm new password",
                text: $authVM.passwordConfirmation,
                isSecure: true,
                onSubmit: onSubmit
            )
            .focused($focusedField, equals: .confirmPassword)
        }
    }
}

private struct UpdateButton: View {
    let title: String
    let isLoading: Bool
    let isDisabled: Bool
    let action: () -> Void
    
    var body: some View {
        AppButton(
            title: title,
            action: action,
            isLoading: isLoading
        )
        .disabled(isDisabled)
    }
}

#Preview {
    NavigationStack {
        AccountSettingsView()
    }
}
