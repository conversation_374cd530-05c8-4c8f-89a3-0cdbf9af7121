//
//  ProfileView.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

enum ProfileDestination: String, Hashable {
    case editProfile = "Edit profile"
    case updateEmail = "Update email"
    case updatePassword = "Update password"
    case deleteAccount = "Delete account"
    case savedChats = "Saved chats"
    case referFriend = "Refer a friend"
    case notification = "Notification"
    case suggestLegend = "Suggest a legend"
}

struct ProfileView: View {
    @ObservedObject var authVM: AuthScreenViewModel
    @State private var showLogoutAlert = false
    @State private var isRefreshing = false

    var body: some View {
        NavigationStack {
            List {
                ProfileHeaderSection(user: authVM.currentUser, authVM: authVM)
                    .listSectionSeparator(.hidden)
                ProfileOptionsSection(showLogoutAlert: $showLogoutAlert, onLogout: handleLogout)
            }
            .listStyle(.plain)
            .navigationTitle("Profile")
            .refreshable {
                await handleRefresh()
            }
        }
        .task {
            await loadUserDataIfNeeded()
        }
    }
    
    // MARK: - Private Methods
    private func loadUserDataIfNeeded() async {
        if authVM.currentUser == nil {
            await authVM.loadCurrentUser()
        }
    }
    
    private func handleLogout() {
        Task { await authVM.logout() }
    }
    
    private func handleRefresh() async {
        isRefreshing = true
        await authVM.loadCurrentUser()
        isRefreshing = false
    }
}

// MARK: - Profile Header Section
private struct ProfileHeaderSection: View {
    let user: UserItem?
    @ObservedObject var authVM: AuthScreenViewModel
    
    var body: some View {
        Section {
            VStack(spacing: 12) {
                profileImage
                userNameText
                userEmailText
                emailVerificationStatus
            }
            .padding(.vertical, 20)
            .frame(maxWidth: .infinity)
        }
    }
    
    private var profileImage: some View {
        AsyncImage(url: URL(string: user?.profileImageUrl ?? "")) { image in
            image
                .resizable()
                .aspectRatio(contentMode: .fill)
        } placeholder: {
            Image(systemName: "person.crop.circle.fill")
                .resizable()
                .foregroundColor(.gray)
        }
        .frame(width: 80, height: 80)
        .clipShape(Circle())
    }
    
    private var userNameText: some View {
        Text(user?.fullName ?? user?.email ?? "Unknown User")
            .font(.title2)
            .fontWeight(.semibold)
    }
    
    @ViewBuilder
    private var userEmailText: some View {
        if let user = user, !user.fullName.isEmpty {
            Text(user.email)
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
    }
    
    @ViewBuilder
    private var emailVerificationStatus: some View {
        if let user = user, !user.emailVerified {
            Button(action: {
                handleResendVerification()
            }) {
                HStack {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .foregroundColor(.orange)
                    Text("Email not verified - Tap to resend")
                        .font(.caption)
                        .foregroundColor(.orange)
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 4)
                .background(Color.orange.opacity(0.1))
                .clipShape(Capsule())
            }
            .buttonStyle(.plain)
            .sensoryFeedback(.success, trigger: authVM.successTrigger)
            .sensoryFeedback(.error, trigger: authVM.errorTrigger)
            .alert("Verification Email Sent", isPresented: $authVM.showSuccessAlert) {
                Button("OK", role: .cancel) {}
            } message: {
                Text(authVM.successMessage)
            }
            .alert("Error", isPresented: $authVM.errorState.showError) {
                Button("OK", role: .cancel) {
                    authVM.errorState = (false, "")
                }
            } message: {
                Text(authVM.errorState.errorMessage)
            }
        }
    }
    
    private func handleResendVerification() {
        Task {
            await authVM.resendEmailVerification()
        }
    }
}

// MARK: - Profile Options Section
private struct ProfileOptionsSection: View {
    @Binding var showLogoutAlert: Bool
    let onLogout: () -> Void
    
    var body: some View {
        Section {
            // Edit Profile
            NavigationLink(destination: EditProfileView()) {
                ProfileItemView(
                    labelText: "Edit profile",
                    labelImage: "person.crop.circle.fill"
                )
            }
            .padding()
            
            // Other Options
            ForEach(optionDestinations, id: \.self) { destination in
                NavigationLink(destination: ProfileDetailView(destination: destination)) {
                    ProfileItemView(
                        labelText: destination.rawValue,
                        labelImage: iconForDestination(destination)
                    )
                }
                .padding()
            }
            
            // Logout Button
            Button(action: { showLogoutAlert = true }) {
                ProfileItemView(
                    labelText: "Logout",
                    labelImage: "arrow.backward.square.fill"
                )
                .foregroundColor(.red)
            }
            .sensoryFeedback(.warning, trigger: showLogoutAlert)
            .padding()
            .alert("Are you sure you want to logout?", isPresented: $showLogoutAlert) {
                Button("Cancel", role: .cancel, action: {})
                Button("Logout", role: .destructive) {
                    // Trigger destructive haptic
                    UINotificationFeedbackGenerator().notificationOccurred(.success)
                    onLogout()
                }
            }
        }
    }
    
    private var optionDestinations: [ProfileDestination] {
        [.savedChats, .referFriend, .notification, .suggestLegend]
    }
    
    private func iconForDestination(_ destination: ProfileDestination) -> String {
        switch destination {
        case .savedChats: return "message.fill"
        case .referFriend: return "arrowshape.turn.up.right.fill"
        case .notification: return "bell.badge.fill"
        case .suggestLegend: return "star.hexagon.fill"
        default: return "questionmark.circle"
        }
    }
}

// MARK: - Edit Profile View
struct EditProfileView: View {
    @StateObject private var authVM = AuthScreenViewModel()
    
    var body: some View {
        List {
            Section(header: Text("Account Management")) {
                ForEach(accountManagementOptions, id: \.destination) { option in
                    NavigationLink(destination: option.view) {
                        ProfileItemView(
                            labelText: option.destination.rawValue,
                            labelImage: option.icon
                        )
                    }
                    .padding(.vertical, 4)
                    .foregroundColor(option.destination == .deleteAccount ? .red : .primary)
                }
            }
        }
        .navigationTitle("Edit Profile")
    }
    
    private var accountManagementOptions: [(destination: ProfileDestination, icon: String, view: AnyView)] {
        [
            (.updateEmail, "envelope", AnyView(UpdateEmailView(authVM: authVM))),
            (.updatePassword, "lock", AnyView(UpdatePasswordView(authVM: authVM))),
            (.deleteAccount, "trash", AnyView(DeleteAccountView(authVM: authVM)))
        ]
    }
}

// MARK: - Reusable Profile Item View
struct ProfileItemView: View {
    let labelText: String
    let labelImage: String
    
    var body: some View {
        Label(labelText, systemImage: labelImage)
    }
}

// MARK: - Profile Detail View
struct ProfileDetailView: View {
    let destination: ProfileDestination
    
    var body: some View {
        VStack {
            Text(destination.rawValue)
                .font(.title)
                .padding()
            Spacer()
        }
        .navigationTitle(destination.rawValue)
        .navigationBarTitleDisplayMode(.large)
    }
}

// MARK: - Delete Account View
struct DeleteAccountView: View {
    @ObservedObject var authVM: AuthScreenViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var showConfirmAlert = false
    @FocusState private var isPasswordFocused: Bool
    
    var body: some View {
        VStack(spacing: 24) {
            deleteAccountHeader
            passwordField
            Spacer()
            deleteButton
        }
        .padding()
        .navigationTitle("Delete Account")
        .sensoryFeedback(.success, trigger: authVM.successTrigger)
        .sensoryFeedback(.error, trigger: authVM.errorTrigger)
        .alert("Success", isPresented: $authVM.showSuccessAlert) {
            Button("OK") {
                UINotificationFeedbackGenerator().notificationOccurred(.success)
                dismiss()
            }
        } message: {
            Text(authVM.successMessage)
        }
        .alert("Error", isPresented: $authVM.errorState.showError) {
            Button("OK", role: .cancel) {
                UINotificationFeedbackGenerator().notificationOccurred(.error)
            }
        } message: {
            Text(authVM.errorState.errorMessage)
        }
    }
    
    // MARK: - Private Views
    private var deleteAccountHeader: some View {
        VStack {
            Text("This action cannot be undone. Please enter your current password to confirm.")
                .multilineTextAlignment(.center)
                .foregroundColor(.secondary)
        }
    }
    
    private var passwordField: some View {
        AppTextField(
            title: "Current Password",
            placeholder: "Enter your current password",
            text: $authVM.currentPassword,
            isSecure: true
        )
        .focused($isPasswordFocused)
    }
    
    private var deleteButton: some View {
        AppButton(
            title: "Delete my account",
            action: { showConfirmAlert = true },
            backgroundColor: .red,
            foregroundColor: .white,
            isLoading: authVM.isLoading
        )
        .opacity(authVM.currentPassword.isEmpty ? 0.3 : 1.0)
        .disabled(authVM.currentPassword.isEmpty)
        .alert("Are you sure?", isPresented: $showConfirmAlert) {
            Button("Cancel", role: .cancel) {
                UIImpactFeedbackGenerator(style: .light).impactOccurred()
            }
            Button("Delete", role: .destructive) {
                UINotificationFeedbackGenerator().notificationOccurred(.warning)
                Task { await authVM.deleteAccount() }
            }
        } message: {
            Text("This action will permanently remove your account and all associated data.")
        }
    }
}

#Preview {
    NavigationStack {
        ProfileView(authVM: AuthScreenViewModel())
    }
}
