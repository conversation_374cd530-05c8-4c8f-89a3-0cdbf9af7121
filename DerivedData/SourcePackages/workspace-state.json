{"object": {"artifacts": [{"kind": "xcframework", "packageRef": {"identity": "abseil-cpp-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/abseil-cpp-binary.git", "name": "abseil-cpp-binary"}, "path": "/Users/<USER>/<PERSON><PERSON>per/Z<PERSON>/DerivedData/SourcePackages/artifacts/abseil-cpp-binary/absl/absl.xcframework", "source": {"checksum": "72d4a03d002a063c6c99e04d9c0310ca830dfb9f39deb9a74ba689d6c1d89ac9", "type": "remote", "url": "https://dl.google.com/firebase/ios/bin/abseil/1.2024072200.0/rc0/absl.zip"}, "targetName": "absl"}, {"kind": "xcframework", "packageRef": {"identity": "firebase-ios-sdk", "kind": "remoteSourceControl", "location": "https://github.com/firebase/firebase-ios-sdk", "name": "firebase-ios-sdk"}, "path": "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/artifacts/firebase-ios-sdk/FirebaseAnalytics/FirebaseAnalytics.xcframework", "source": {"checksum": "b86d668ff8b5e0df396d1a5711632b542247e03c8dda8ab4722185090d47300c", "type": "remote", "url": "https://dl.google.com/firebase/ios/swiftpm/11.14.0/FirebaseAnalytics.zip"}, "targetName": "FirebaseAnalytics"}, {"kind": "xcframework", "packageRef": {"identity": "firebase-ios-sdk", "kind": "remoteSourceControl", "location": "https://github.com/firebase/firebase-ios-sdk", "name": "firebase-ios-sdk"}, "path": "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/artifacts/firebase-ios-sdk/FirebaseFirestoreInternal/FirebaseFirestoreInternal.xcframework", "source": {"checksum": "badb559c67f683d546873051642db7eaab3598e50f8095dc15d965d63a695145", "type": "remote", "url": "https://dl.google.com/firebase/ios/bin/firestore/11.13.0/rc0/FirebaseFirestoreInternal.zip"}, "targetName": "FirebaseFirestoreInternal"}, {"kind": "xcframework", "packageRef": {"identity": "google-ads-on-device-conversion-ios-sdk", "kind": "remoteSourceControl", "location": "https://github.com/googleads/google-ads-on-device-conversion-ios-sdk", "name": "google-ads-on-device-conversion-ios-sdk"}, "path": "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/artifacts/google-ads-on-device-conversion-ios-sdk/GoogleAdsOnDeviceConversion/GoogleAdsOnDeviceConversion.xcframework", "source": {"checksum": "bb7f0d521c5eea9bcc16a93dcdb33099b1b873956af342f802e1dcf27cdced4f", "type": "remote", "url": "https://dl.google.com/firebase/ios/appads/2.1.0/GoogleAdsOnDeviceConversion.zip"}, "targetName": "GoogleAdsOnDeviceConversion"}, {"kind": "xcframework", "packageRef": {"identity": "googleappmeasurement", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleAppMeasurement.git", "name": "GoogleAppMeasurement"}, "path": "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/artifacts/googleappmeasurement/GoogleAppMeasurement/GoogleAppMeasurement.xcframework", "source": {"checksum": "f8accf50ff3ccba22fa19b572aac348cf9d2b72f57261c02fa35a173efb6f960", "type": "remote", "url": "https://dl.google.com/firebase/ios/swiftpm/11.14.0/GoogleAppMeasurement.zip"}, "targetName": "GoogleAppMeasurement"}, {"kind": "xcframework", "packageRef": {"identity": "googleappmeasurement", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleAppMeasurement.git", "name": "GoogleAppMeasurement"}, "path": "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/artifacts/googleappmeasurement/GoogleAppMeasurementOnDeviceConversion/GoogleAppMeasurementOnDeviceConversion.xcframework", "source": {"checksum": "10550f6ec6098ecfb9bb685fcb6bdf5499807af05cfbecb21a7f31fa8f03824e", "type": "remote", "url": "https://dl.google.com/firebase/ios/swiftpm/11.14.0/GoogleAppMeasurementOnDeviceConversion.zip"}, "targetName": "GoogleAppMeasurementOnDeviceConversion"}, {"kind": "xcframework", "packageRef": {"identity": "googleappmeasurement", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleAppMeasurement.git", "name": "GoogleAppMeasurement"}, "path": "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/artifacts/googleappmeasurement/GoogleAppMeasurementIdentitySupport/GoogleAppMeasurementIdentitySupport.xcframework", "source": {"checksum": "d5367d077b9cdffd2ec05cc80224f04531b1ce469826ed6b9e84e0789bcd75c8", "type": "remote", "url": "https://dl.google.com/firebase/ios/swiftpm/11.14.0/GoogleAppMeasurementIdentitySupport.zip"}, "targetName": "GoogleAppMeasurementIdentitySupport"}, {"kind": "xcframework", "packageRef": {"identity": "grpc-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/grpc-binary.git", "name": "grpc-binary"}, "path": "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/artifacts/grpc-binary/openssl_grpc/openssl_grpc.xcframework", "source": {"checksum": "67b9cc1763686cd6be6ee98b85a51c1140a05a4efc7f0b39da4c4f5a78c4f81c", "type": "remote", "url": "https://dl.google.com/firebase/ios/bin/grpc/1.69.0/rc0/openssl_grpc.zip"}, "targetName": "openssl_grpc"}, {"kind": "xcframework", "packageRef": {"identity": "grpc-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/grpc-binary.git", "name": "grpc-binary"}, "path": "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/artifacts/grpc-binary/grpc/grpc.xcframework", "source": {"checksum": "1b9846aba884d0e95d9054646637e2d782d6d1acd7916287ad34fba5ce6b915a", "type": "remote", "url": "https://dl.google.com/firebase/ios/bin/grpc/1.69.0/rc0/grpc.zip"}, "targetName": "grpc"}, {"kind": "xcframework", "packageRef": {"identity": "grpc-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/grpc-binary.git", "name": "grpc-binary"}, "path": "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/artifacts/grpc-binary/grpcpp/grpcpp.xcframework", "source": {"checksum": "7f8fcd89a8aef73994a0ffc9a3be2c29f9e49278e1e1b02061a24d1a4c25ed20", "type": "remote", "url": "https://dl.google.com/firebase/ios/bin/grpc/1.69.0/rc0/grpcpp.zip"}, "targetName": "grpcpp"}], "dependencies": [{"basedOn": null, "packageRef": {"identity": "abseil-cpp-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/abseil-cpp-binary.git", "name": "abseil-cpp-binary"}, "state": {"checkoutState": {"revision": "bbe8b69694d7873315fd3a4ad41efe043e1c07c5", "version": "1.2024072200.0"}, "name": "sourceControlCheckout"}, "subpath": "abseil-cpp-binary"}, {"basedOn": null, "packageRef": {"identity": "app-check", "kind": "remoteSourceControl", "location": "https://github.com/google/app-check.git", "name": "app-check"}, "state": {"checkoutState": {"revision": "61b85103a1aeed8218f17c794687781505fbbef5", "version": "11.2.0"}, "name": "sourceControlCheckout"}, "subpath": "app-check"}, {"basedOn": null, "packageRef": {"identity": "appauth-ios", "kind": "remoteSourceControl", "location": "https://github.com/openid/AppAuth-iOS.git", "name": "AppAuth-iOS"}, "state": {"checkoutState": {"revision": "2781038865a80e2c425a1da12cc1327bcd56501f", "version": "1.7.6"}, "name": "sourceControlCheckout"}, "subpath": "AppAuth-iOS"}, {"basedOn": null, "packageRef": {"identity": "firebase-ios-sdk", "kind": "remoteSourceControl", "location": "https://github.com/firebase/firebase-ios-sdk", "name": "firebase-ios-sdk"}, "state": {"checkoutState": {"revision": "45d327fcbe7793747295346c2209ad419bdead74", "version": "11.14.0"}, "name": "sourceControlCheckout"}, "subpath": "firebase-ios-sdk"}, {"basedOn": null, "packageRef": {"identity": "google-ads-on-device-conversion-ios-sdk", "kind": "remoteSourceControl", "location": "https://github.com/googleads/google-ads-on-device-conversion-ios-sdk", "name": "google-ads-on-device-conversion-ios-sdk"}, "state": {"checkoutState": {"revision": "428d8bb138e00f9a3f4f61cc6cd8863607524f65", "version": "2.1.0"}, "name": "sourceControlCheckout"}, "subpath": "google-ads-on-device-conversion-ios-sdk"}, {"basedOn": null, "packageRef": {"identity": "googleappmeasurement", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleAppMeasurement.git", "name": "GoogleAppMeasurement"}, "state": {"checkoutState": {"revision": "406f72d0d5e9445fd1cf782db3e9e338cee2bed4", "version": "11.14.0"}, "name": "sourceControlCheckout"}, "subpath": "GoogleAppMeasurement"}, {"basedOn": null, "packageRef": {"identity": "googledatatransport", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleDataTransport.git", "name": "GoogleDataTransport"}, "state": {"checkoutState": {"revision": "617af071af9aa1d6a091d59a202910ac482128f9", "version": "10.1.0"}, "name": "sourceControlCheckout"}, "subpath": "GoogleDataTransport"}, {"basedOn": null, "packageRef": {"identity": "googlesignin-ios", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleSignIn-iOS", "name": "GoogleSignIn-iOS"}, "state": {"checkoutState": {"revision": "65fb3f1aa6ffbfdc79c4e22178a55cd91561f5e9", "version": "8.0.0"}, "name": "sourceControlCheckout"}, "subpath": "GoogleSignIn-iOS"}, {"basedOn": null, "packageRef": {"identity": "googleutilities", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleUtilities.git", "name": "GoogleUtilities"}, "state": {"checkoutState": {"revision": "60da361632d0de02786f709bdc0c4df340f7613e", "version": "8.1.0"}, "name": "sourceControlCheckout"}, "subpath": "GoogleUtilities"}, {"basedOn": null, "packageRef": {"identity": "grpc-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/grpc-binary.git", "name": "grpc-binary"}, "state": {"checkoutState": {"revision": "cc0001a0cf963aa40501d9c2b181e7fc9fd8ec71", "version": "1.69.0"}, "name": "sourceControlCheckout"}, "subpath": "grpc-binary"}, {"basedOn": null, "packageRef": {"identity": "gtm-session-fetcher", "kind": "remoteSourceControl", "location": "https://github.com/google/gtm-session-fetcher.git", "name": "gtm-session-fetcher"}, "state": {"checkoutState": {"revision": "a2ab612cb980066ee56d90d60d8462992c07f24b", "version": "3.5.0"}, "name": "sourceControlCheckout"}, "subpath": "gtm-session-fetcher"}, {"basedOn": null, "packageRef": {"identity": "gtmappauth", "kind": "remoteSourceControl", "location": "https://github.com/google/GTMAppAuth.git", "name": "GTMAppAuth"}, "state": {"checkoutState": {"revision": "5d7d66f647400952b1758b230e019b07c0b4b22a", "version": "4.1.1"}, "name": "sourceControlCheckout"}, "subpath": "GTMAppAuth"}, {"basedOn": null, "packageRef": {"identity": "interop-ios-for-google-sdks", "kind": "remoteSourceControl", "location": "https://github.com/google/interop-ios-for-google-sdks.git", "name": "interop-ios-for-google-sdks"}, "state": {"checkoutState": {"revision": "040d087ac2267d2ddd4cca36c757d1c6a05fdbfe", "version": "101.0.0"}, "name": "sourceControlCheckout"}, "subpath": "interop-ios-for-google-sdks"}, {"basedOn": null, "packageRef": {"identity": "leveldb", "kind": "remoteSourceControl", "location": "https://github.com/firebase/leveldb.git", "name": "leveldb"}, "state": {"checkoutState": {"revision": "a0bc79961d7be727d258d33d5a6b2f1023270ba1", "version": "1.22.5"}, "name": "sourceControlCheckout"}, "subpath": "leveldb"}, {"basedOn": null, "packageRef": {"identity": "nanopb", "kind": "remoteSourceControl", "location": "https://github.com/firebase/nanopb.git", "name": "nanopb"}, "state": {"checkoutState": {"revision": "b7e1104502eca3a213b46303391ca4d3bc8ddec1", "version": "2.30910.0"}, "name": "sourceControlCheckout"}, "subpath": "nanopb"}, {"basedOn": null, "packageRef": {"identity": "promises", "kind": "remoteSourceControl", "location": "https://github.com/google/promises.git", "name": "promises"}, "state": {"checkoutState": {"revision": "540318ecedd63d883069ae7f1ed811a2df00b6ac", "version": "2.4.0"}, "name": "sourceControlCheckout"}, "subpath": "promises"}, {"basedOn": null, "packageRef": {"identity": "swift-protobuf", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-protobuf.git", "name": "swift-protobuf"}, "state": {"checkoutState": {"revision": "102a647b573f60f73afdce5613a51d71349fe507", "version": "1.30.0"}, "name": "sourceControlCheckout"}, "subpath": "swift-protobuf"}], "prebuilts": []}, "version": 7}