moduledependenciestarget: \
  /Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/AppAuth.modulemap \
  /Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/AppAuth-iOS/Sources/AppAuth/iOS/OIDAuthState+IOS.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk/usr/include/TargetConditionals.modulemap \
  /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk/System/Library/Frameworks/UIKit.framework/Modules/module.modulemap \
  /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk/System/Library/Frameworks/Foundation.framework/Modules/module.modulemap \
  /Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/AppAuthCore.modulemap \
  /Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/AppAuth-iOS/Sources/AppAuth/iOS/OIDAuthorizationService+IOS.h \
  /Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/AppAuth-iOS/Sources/AppAuth/iOS/OIDExternalUserAgentCatalyst.h \
  /Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/AppAuth-iOS/Sources/AppAuth/iOS/OIDExternalUserAgentIOS.h \
  /Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/AppAuth-iOS/Sources/AppAuth/iOS/OIDExternalUserAgentIOSCustomBrowser.h \
  /Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/AppAuth-iOS/Sources/AppAuth/macOS/LoopbackHTTPServer/OIDLoopbackHTTPServer.h \
  /Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/AppAuth-iOS/Sources/AppAuth/macOS/OIDAuthState+Mac.h \
  /Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/AppAuth-iOS/Sources/AppAuth/macOS/OIDAuthorizationService+Mac.h \
  /Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/AppAuth-iOS/Sources/AppAuth/macOS/OIDExternalUserAgentMac.h \
  /Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/AppAuth-iOS/Sources/AppAuth/macOS/OIDRedirectHTTPHandler.h
