/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseStorage/Sources/AsyncAwait.swift
/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseStorage/Sources/Internal/StorageDeleteTask.swift
/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseStorage/Sources/Internal/StorageFetcherService.swift
/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseStorage/Sources/Internal/StorageGetDownloadURLTask.swift
/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseStorage/Sources/Internal/StorageGetMetadataTask.swift
/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseStorage/Sources/Internal/StorageInternalTask.swift
/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseStorage/Sources/Internal/StorageListTask.swift
/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseStorage/Sources/Internal/StoragePath.swift
/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseStorage/Sources/Internal/StorageTaskState.swift
/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseStorage/Sources/Internal/StorageTokenAuthorizer.swift
/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseStorage/Sources/Internal/StorageUpdateMetadataTask.swift
/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseStorage/Sources/Internal/StorageUtils.swift
/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseStorage/Sources/Result.swift
/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseStorage/Sources/Storage.swift
/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseStorage/Sources/StorageConstants.swift
/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseStorage/Sources/StorageDownloadTask.swift
/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseStorage/Sources/StorageError.swift
/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseStorage/Sources/StorageListResult.swift
/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseStorage/Sources/StorageMetadata.swift
/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseStorage/Sources/StorageObservableTask.swift
/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseStorage/Sources/StorageReference.swift
/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseStorage/Sources/StorageTask.swift
/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseStorage/Sources/StorageTaskSnapshot.swift
/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseStorage/Sources/StorageUploadTask.swift
