-target arm64-apple-ios12.0-simulator '-std=c99' -fobjc-arc -fmodules -gmodules '-fmodules-cache-path=/Users/<USER>/Developer/Zadrz/DerivedData/ModuleCache.noindex' '-fmodule-name=GoogleDataTransport' -fpascal-strings -O0 -DSWIFT_PACKAGE '-DDEBUG=1' '-DGDTCOR_VERSION=0.0.1' '-DPB_FIELD_32BIT=1' '-DPB_NO_PACKED_STRUCTS=1' '-DPB_ENABLE_MALLOC=1' '-DOBJC_OLD_DISPATCH_PROTOTYPES=1' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk -g '-fobjc-abi-version=2' -fobjc-legacy-dispatch -fprofile-instr-generate -fcoverage-mapping -I/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/nanopb/spm_headers -I/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/promises/Sources/FBLPromises/include -I/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator/include -I/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/GoogleDataTransport/GoogleDataTransport/GDTCORLibrary/Public -I/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/GoogleDataTransport -I/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/GoogleDataTransport.build/Debug-iphonesimulator/GoogleDataTransport.build/DerivedSources-normal/arm64 -I/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/GoogleDataTransport.build/Debug-iphonesimulator/GoogleDataTransport.build/DerivedSources/arm64 -I/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/GoogleDataTransport.build/Debug-iphonesimulator/GoogleDataTransport.build/DerivedSources -F/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator -iframework /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Library/Frameworks -iframework /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk/Developer/Library/Frameworks '-fmodule-map-file=/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/nanopb.modulemap' -DXcode