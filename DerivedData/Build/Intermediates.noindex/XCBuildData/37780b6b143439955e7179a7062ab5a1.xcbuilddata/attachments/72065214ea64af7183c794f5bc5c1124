-target arm64-apple-ios12.0-simulator -fobjc-arc -fmodules -gmodules '-fmodules-cache-path=/Users/<USER>/Developer/Zadrz/DerivedData/ModuleCache.noindex' '-fmodule-name=AppAuthCore' -fpascal-strings -O0 -DSWIFT_PACKAGE '-DDEBUG=1' '-DOBJC_OLD_DISPATCH_PROTOTYPES=1' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk -g '-fobjc-abi-version=2' -fobjc-legacy-dispatch -fprofile-instr-generate -fcoverage-mapping -I/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator/include -I/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/AppAuth-iOS/Sources/AppAuthCore -I/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/AppAuth.build/Debug-iphonesimulator/AppAuthCore.build/DerivedSources-normal/arm64 -I/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/AppAuth.build/Debug-iphonesimulator/AppAuthCore.build/DerivedSources/arm64 -I/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/AppAuth.build/Debug-iphonesimulator/AppAuthCore.build/DerivedSources -F/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator -iframework /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Library/Frameworks -iframework /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk/Developer/Library/Frameworks -DXcode