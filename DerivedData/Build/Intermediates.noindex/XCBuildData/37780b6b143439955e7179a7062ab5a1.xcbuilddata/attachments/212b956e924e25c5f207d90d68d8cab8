{"": {"diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FirebaseAuth-master.dia", "emit-module-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FirebaseAuth-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FirebaseAuth-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FirebaseAuth-master.swiftdeps"}, "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/DerivedSources/resource_bundle_accessor.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/resource_bundle_accessor.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/resource_bundle_accessor.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/resource_bundle_accessor.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/resource_bundle_accessor.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/resource_bundle_accessor.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/resource_bundle_accessor.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/resource_bundle_accessor.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/resource_bundle_accessor~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/ActionCode/ActionCodeInfo.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ActionCodeInfo.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ActionCodeInfo.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ActionCodeInfo.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ActionCodeInfo.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ActionCodeInfo.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ActionCodeInfo.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ActionCodeInfo.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ActionCodeInfo~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/ActionCode/ActionCodeOperation.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ActionCodeOperation.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ActionCodeOperation.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ActionCodeOperation.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ActionCodeOperation.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ActionCodeOperation.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ActionCodeOperation.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ActionCodeOperation.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ActionCodeOperation~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/ActionCode/ActionCodeSettings.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ActionCodeSettings.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ActionCodeSettings.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ActionCodeSettings.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ActionCodeSettings.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ActionCodeSettings.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ActionCodeSettings.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ActionCodeSettings.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ActionCodeSettings~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/ActionCode/ActionCodeURL.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ActionCodeURL.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ActionCodeURL.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ActionCodeURL.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ActionCodeURL.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ActionCodeURL.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ActionCodeURL.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ActionCodeURL.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ActionCodeURL~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Auth/Auth.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/Auth.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/Auth.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/Auth.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/Auth.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/Auth.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/Auth.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/Auth.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/Auth~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Auth/AuthComponent.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthComponent.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthComponent.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthComponent.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthComponent.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthComponent.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthComponent.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthComponent.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthComponent~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Auth/AuthDataResult.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthDataResult.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthDataResult.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthDataResult.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthDataResult.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthDataResult.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthDataResult.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthDataResult.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthDataResult~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Auth/AuthDispatcher.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthDispatcher.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthDispatcher.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthDispatcher.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthDispatcher.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthDispatcher.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthDispatcher.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthDispatcher.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthDispatcher~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Auth/AuthGlobalWorkQueue.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthGlobalWorkQueue.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthGlobalWorkQueue.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthGlobalWorkQueue.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthGlobalWorkQueue.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthGlobalWorkQueue.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthGlobalWorkQueue.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthGlobalWorkQueue.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthGlobalWorkQueue~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Auth/AuthOperationType.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthOperationType.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthOperationType.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthOperationType.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthOperationType.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthOperationType.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthOperationType.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthOperationType.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthOperationType~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Auth/AuthSettings.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthSettings.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthSettings.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthSettings.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthSettings.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthSettings.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthSettings.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthSettings.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthSettings~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Auth/AuthTokenResult.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthTokenResult.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthTokenResult.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthTokenResult.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthTokenResult.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthTokenResult.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthTokenResult.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthTokenResult.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthTokenResult~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/AuthProvider/AuthCredential.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthCredential.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthCredential.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthCredential.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthCredential.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthCredential.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthCredential.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthCredential.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthCredential~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/AuthProvider/AuthProviderID.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProviderID.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProviderID.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProviderID.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProviderID.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProviderID.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProviderID.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProviderID.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProviderID~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/AuthProvider/EmailAuthProvider.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/EmailAuthProvider.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/EmailAuthProvider.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/EmailAuthProvider.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/EmailAuthProvider.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/EmailAuthProvider.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/EmailAuthProvider.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/EmailAuthProvider.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/EmailAuthProvider~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/AuthProvider/FacebookAuthProvider.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FacebookAuthProvider.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FacebookAuthProvider.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FacebookAuthProvider.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FacebookAuthProvider.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FacebookAuthProvider.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FacebookAuthProvider.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FacebookAuthProvider.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FacebookAuthProvider~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/AuthProvider/FederatedAuthProvider.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FederatedAuthProvider.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FederatedAuthProvider.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FederatedAuthProvider.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FederatedAuthProvider.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FederatedAuthProvider.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FederatedAuthProvider.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FederatedAuthProvider.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FederatedAuthProvider~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/AuthProvider/GameCenterAuthProvider.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GameCenterAuthProvider.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GameCenterAuthProvider.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GameCenterAuthProvider.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GameCenterAuthProvider.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GameCenterAuthProvider.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GameCenterAuthProvider.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GameCenterAuthProvider.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GameCenterAuthProvider~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/AuthProvider/GitHubAuthProvider.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GitHubAuthProvider.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GitHubAuthProvider.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GitHubAuthProvider.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GitHubAuthProvider.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GitHubAuthProvider.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GitHubAuthProvider.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GitHubAuthProvider.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GitHubAuthProvider~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/AuthProvider/GoogleAuthProvider.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GoogleAuthProvider.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GoogleAuthProvider.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GoogleAuthProvider.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GoogleAuthProvider.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GoogleAuthProvider.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GoogleAuthProvider.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GoogleAuthProvider.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GoogleAuthProvider~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/AuthProvider/OAuthCredential.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/OAuthCredential.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/OAuthCredential.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/OAuthCredential.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/OAuthCredential.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/OAuthCredential.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/OAuthCredential.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/OAuthCredential.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/OAuthCredential~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/AuthProvider/OAuthProvider.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/OAuthProvider.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/OAuthProvider.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/OAuthProvider.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/OAuthProvider.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/OAuthProvider.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/OAuthProvider.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/OAuthProvider.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/OAuthProvider~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/AuthProvider/PhoneAuthCredential.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneAuthCredential.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneAuthCredential.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneAuthCredential.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneAuthCredential.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneAuthCredential.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneAuthCredential.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneAuthCredential.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneAuthCredential~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/AuthProvider/PhoneAuthProvider.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneAuthProvider.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneAuthProvider.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneAuthProvider.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneAuthProvider.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneAuthProvider.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneAuthProvider.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneAuthProvider.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneAuthProvider~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/AuthProvider/TwitterAuthProvider.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TwitterAuthProvider.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TwitterAuthProvider.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TwitterAuthProvider.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TwitterAuthProvider.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TwitterAuthProvider.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TwitterAuthProvider.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TwitterAuthProvider.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TwitterAuthProvider~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/AuthBackend.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthBackend.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthBackend.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthBackend.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthBackend.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthBackend.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthBackend.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthBackend.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthBackend~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/AuthBackendRPCIssuer.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthBackendRPCIssuer.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthBackendRPCIssuer.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthBackendRPCIssuer.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthBackendRPCIssuer.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthBackendRPCIssuer.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthBackendRPCIssuer.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthBackendRPCIssuer.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthBackendRPCIssuer~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/AuthRPCRequest.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthRPCRequest.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthRPCRequest.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthRPCRequest.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthRPCRequest.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthRPCRequest.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthRPCRequest.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthRPCRequest.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthRPCRequest~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/AuthRPCResponse.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthRPCResponse.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthRPCResponse.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthRPCResponse.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthRPCResponse.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthRPCResponse.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthRPCResponse.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthRPCResponse.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthRPCResponse~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/AuthRequestConfiguration.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthRequestConfiguration.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthRequestConfiguration.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthRequestConfiguration.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthRequestConfiguration.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthRequestConfiguration.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthRequestConfiguration.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthRequestConfiguration.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthRequestConfiguration~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/IdentityToolkitRequest.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/IdentityToolkitRequest.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/IdentityToolkitRequest.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/IdentityToolkitRequest.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/IdentityToolkitRequest.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/IdentityToolkitRequest.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/IdentityToolkitRequest.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/IdentityToolkitRequest.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/IdentityToolkitRequest~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/AuthMFAResponse.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthMFAResponse.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthMFAResponse.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthMFAResponse.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthMFAResponse.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthMFAResponse.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthMFAResponse.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthMFAResponse.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthMFAResponse~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/CreateAuthURIRequest.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/CreateAuthURIRequest.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/CreateAuthURIRequest.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/CreateAuthURIRequest.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/CreateAuthURIRequest.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/CreateAuthURIRequest.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/CreateAuthURIRequest.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/CreateAuthURIRequest.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/CreateAuthURIRequest~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/CreateAuthURIResponse.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/CreateAuthURIResponse.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/CreateAuthURIResponse.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/CreateAuthURIResponse.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/CreateAuthURIResponse.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/CreateAuthURIResponse.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/CreateAuthURIResponse.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/CreateAuthURIResponse.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/CreateAuthURIResponse~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/DeleteAccountRequest.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/DeleteAccountRequest.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/DeleteAccountRequest.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/DeleteAccountRequest.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/DeleteAccountRequest.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/DeleteAccountRequest.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/DeleteAccountRequest.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/DeleteAccountRequest.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/DeleteAccountRequest~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/DeleteAccountResponse.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/DeleteAccountResponse.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/DeleteAccountResponse.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/DeleteAccountResponse.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/DeleteAccountResponse.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/DeleteAccountResponse.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/DeleteAccountResponse.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/DeleteAccountResponse.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/DeleteAccountResponse~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/EmailLinkSignInRequest.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/EmailLinkSignInRequest.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/EmailLinkSignInRequest.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/EmailLinkSignInRequest.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/EmailLinkSignInRequest.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/EmailLinkSignInRequest.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/EmailLinkSignInRequest.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/EmailLinkSignInRequest.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/EmailLinkSignInRequest~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/EmailLinkSignInResponse.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/EmailLinkSignInResponse.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/EmailLinkSignInResponse.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/EmailLinkSignInResponse.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/EmailLinkSignInResponse.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/EmailLinkSignInResponse.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/EmailLinkSignInResponse.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/EmailLinkSignInResponse.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/EmailLinkSignInResponse~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/GetAccountInfoRequest.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetAccountInfoRequest.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetAccountInfoRequest.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetAccountInfoRequest.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetAccountInfoRequest.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetAccountInfoRequest.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetAccountInfoRequest.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetAccountInfoRequest.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetAccountInfoRequest~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/GetAccountInfoResponse.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetAccountInfoResponse.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetAccountInfoResponse.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetAccountInfoResponse.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetAccountInfoResponse.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetAccountInfoResponse.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetAccountInfoResponse.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetAccountInfoResponse.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetAccountInfoResponse~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/GetOOBConfirmationCodeRequest.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetOOBConfirmationCodeRequest.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetOOBConfirmationCodeRequest.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetOOBConfirmationCodeRequest.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetOOBConfirmationCodeRequest.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetOOBConfirmationCodeRequest.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetOOBConfirmationCodeRequest.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetOOBConfirmationCodeRequest.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetOOBConfirmationCodeRequest~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/GetOOBConfirmationCodeResponse.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetOOBConfirmationCodeResponse.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetOOBConfirmationCodeResponse.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetOOBConfirmationCodeResponse.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetOOBConfirmationCodeResponse.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetOOBConfirmationCodeResponse.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetOOBConfirmationCodeResponse.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetOOBConfirmationCodeResponse.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetOOBConfirmationCodeResponse~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/GetProjectConfigRequest.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetProjectConfigRequest.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetProjectConfigRequest.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetProjectConfigRequest.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetProjectConfigRequest.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetProjectConfigRequest.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetProjectConfigRequest.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetProjectConfigRequest.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetProjectConfigRequest~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/GetProjectConfigResponse.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetProjectConfigResponse.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetProjectConfigResponse.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetProjectConfigResponse.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetProjectConfigResponse.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetProjectConfigResponse.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetProjectConfigResponse.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetProjectConfigResponse.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetProjectConfigResponse~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/GetRecaptchaConfigRequest.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetRecaptchaConfigRequest.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetRecaptchaConfigRequest.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetRecaptchaConfigRequest.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetRecaptchaConfigRequest.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetRecaptchaConfigRequest.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetRecaptchaConfigRequest.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetRecaptchaConfigRequest.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetRecaptchaConfigRequest~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/GetRecaptchaConfigResponse.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetRecaptchaConfigResponse.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetRecaptchaConfigResponse.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetRecaptchaConfigResponse.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetRecaptchaConfigResponse.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetRecaptchaConfigResponse.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetRecaptchaConfigResponse.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetRecaptchaConfigResponse.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/GetRecaptchaConfigResponse~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/Enroll/FinalizeMFAEnrollmentRequest.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FinalizeMFAEnrollmentRequest.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FinalizeMFAEnrollmentRequest.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FinalizeMFAEnrollmentRequest.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FinalizeMFAEnrollmentRequest.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FinalizeMFAEnrollmentRequest.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FinalizeMFAEnrollmentRequest.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FinalizeMFAEnrollmentRequest.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FinalizeMFAEnrollmentRequest~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/Enroll/FinalizeMFAEnrollmentResponse.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FinalizeMFAEnrollmentResponse.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FinalizeMFAEnrollmentResponse.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FinalizeMFAEnrollmentResponse.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FinalizeMFAEnrollmentResponse.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FinalizeMFAEnrollmentResponse.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FinalizeMFAEnrollmentResponse.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FinalizeMFAEnrollmentResponse.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FinalizeMFAEnrollmentResponse~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/Enroll/StartMFAEnrollmentRequest.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/StartMFAEnrollmentRequest.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/StartMFAEnrollmentRequest.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/StartMFAEnrollmentRequest.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/StartMFAEnrollmentRequest.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/StartMFAEnrollmentRequest.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/StartMFAEnrollmentRequest.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/StartMFAEnrollmentRequest.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/StartMFAEnrollmentRequest~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/Enroll/StartMFAEnrollmentResponse.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/StartMFAEnrollmentResponse.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/StartMFAEnrollmentResponse.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/StartMFAEnrollmentResponse.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/StartMFAEnrollmentResponse.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/StartMFAEnrollmentResponse.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/StartMFAEnrollmentResponse.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/StartMFAEnrollmentResponse.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/StartMFAEnrollmentResponse~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/SignIn/FinalizeMFASignInRequest.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FinalizeMFASignInRequest.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FinalizeMFASignInRequest.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FinalizeMFASignInRequest.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FinalizeMFASignInRequest.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FinalizeMFASignInRequest.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FinalizeMFASignInRequest.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FinalizeMFASignInRequest.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FinalizeMFASignInRequest~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/SignIn/FinalizeMFASignInResponse.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FinalizeMFASignInResponse.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FinalizeMFASignInResponse.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FinalizeMFASignInResponse.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FinalizeMFASignInResponse.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FinalizeMFASignInResponse.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FinalizeMFASignInResponse.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FinalizeMFASignInResponse.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FinalizeMFASignInResponse~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/SignIn/StartMFASignInRequest.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/StartMFASignInRequest.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/StartMFASignInRequest.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/StartMFASignInRequest.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/StartMFASignInRequest.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/StartMFASignInRequest.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/StartMFASignInRequest.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/StartMFASignInRequest.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/StartMFASignInRequest~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/SignIn/StartMFASignInResponse.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/StartMFASignInResponse.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/StartMFASignInResponse.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/StartMFASignInResponse.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/StartMFASignInResponse.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/StartMFASignInResponse.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/StartMFASignInResponse.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/StartMFASignInResponse.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/StartMFASignInResponse~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/Unenroll/WithdrawMFARequest.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/WithdrawMFARequest.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/WithdrawMFARequest.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/WithdrawMFARequest.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/WithdrawMFARequest.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/WithdrawMFARequest.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/WithdrawMFARequest.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/WithdrawMFARequest.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/WithdrawMFARequest~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/Unenroll/WithdrawMFAResponse.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/WithdrawMFAResponse.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/WithdrawMFAResponse.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/WithdrawMFAResponse.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/WithdrawMFAResponse.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/WithdrawMFAResponse.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/WithdrawMFAResponse.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/WithdrawMFAResponse.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/WithdrawMFAResponse~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/Proto/AuthProto.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProto.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProto.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProto.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProto.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProto.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProto.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProto.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProto~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/Proto/AuthProtoMFAEnrollment.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoMFAEnrollment.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoMFAEnrollment.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoMFAEnrollment.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoMFAEnrollment.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoMFAEnrollment.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoMFAEnrollment.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoMFAEnrollment.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoMFAEnrollment~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/Proto/Phone/AuthProtoFinalizeMFAPhoneRequestInfo.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFAPhoneRequestInfo.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFAPhoneRequestInfo.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFAPhoneRequestInfo.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFAPhoneRequestInfo.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFAPhoneRequestInfo.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFAPhoneRequestInfo.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFAPhoneRequestInfo.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFAPhoneRequestInfo~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/Proto/Phone/AuthProtoFinalizeMFAPhoneResponseInfo.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFAPhoneResponseInfo.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFAPhoneResponseInfo.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFAPhoneResponseInfo.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFAPhoneResponseInfo.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFAPhoneResponseInfo.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFAPhoneResponseInfo.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFAPhoneResponseInfo.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFAPhoneResponseInfo~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/Proto/Phone/AuthProtoStartMFAPhoneRequestInfo.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoStartMFAPhoneRequestInfo.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoStartMFAPhoneRequestInfo.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoStartMFAPhoneRequestInfo.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoStartMFAPhoneRequestInfo.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoStartMFAPhoneRequestInfo.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoStartMFAPhoneRequestInfo.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoStartMFAPhoneRequestInfo.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoStartMFAPhoneRequestInfo~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/Proto/Phone/AuthProtoStartMFAPhoneResponseInfo.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoStartMFAPhoneResponseInfo.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoStartMFAPhoneResponseInfo.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoStartMFAPhoneResponseInfo.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoStartMFAPhoneResponseInfo.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoStartMFAPhoneResponseInfo.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoStartMFAPhoneResponseInfo.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoStartMFAPhoneResponseInfo.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoStartMFAPhoneResponseInfo~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/Proto/TOTP/AuthProtoFinalizeMFATOTPEnrollmentRequestInfo.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFATOTPEnrollmentRequestInfo.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFATOTPEnrollmentRequestInfo.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFATOTPEnrollmentRequestInfo.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFATOTPEnrollmentRequestInfo.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFATOTPEnrollmentRequestInfo.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFATOTPEnrollmentRequestInfo.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFATOTPEnrollmentRequestInfo.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFATOTPEnrollmentRequestInfo~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/Proto/TOTP/AuthProtoFinalizeMFATOTPEnrollmentResponseInfo.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFATOTPEnrollmentResponseInfo.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFATOTPEnrollmentResponseInfo.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFATOTPEnrollmentResponseInfo.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFATOTPEnrollmentResponseInfo.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFATOTPEnrollmentResponseInfo.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFATOTPEnrollmentResponseInfo.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFATOTPEnrollmentResponseInfo.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFATOTPEnrollmentResponseInfo~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/Proto/TOTP/AuthProtoFinalizeMFATOTPSignInRequestInfo.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFATOTPSignInRequestInfo.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFATOTPSignInRequestInfo.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFATOTPSignInRequestInfo.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFATOTPSignInRequestInfo.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFATOTPSignInRequestInfo.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFATOTPSignInRequestInfo.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFATOTPSignInRequestInfo.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoFinalizeMFATOTPSignInRequestInfo~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/Proto/TOTP/AuthProtoStartMFATOTPEnrollmentRequestInfo.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoStartMFATOTPEnrollmentRequestInfo.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoStartMFATOTPEnrollmentRequestInfo.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoStartMFATOTPEnrollmentRequestInfo.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoStartMFATOTPEnrollmentRequestInfo.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoStartMFATOTPEnrollmentRequestInfo.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoStartMFATOTPEnrollmentRequestInfo.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoStartMFATOTPEnrollmentRequestInfo.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoStartMFATOTPEnrollmentRequestInfo~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/Proto/TOTP/AuthProtoStartMFATOTPEnrollmentResponseInfo.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoStartMFATOTPEnrollmentResponseInfo.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoStartMFATOTPEnrollmentResponseInfo.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoStartMFATOTPEnrollmentResponseInfo.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoStartMFATOTPEnrollmentResponseInfo.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoStartMFATOTPEnrollmentResponseInfo.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoStartMFATOTPEnrollmentResponseInfo.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoStartMFATOTPEnrollmentResponseInfo.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthProtoStartMFATOTPEnrollmentResponseInfo~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/ResetPasswordRequest.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ResetPasswordRequest.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ResetPasswordRequest.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ResetPasswordRequest.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ResetPasswordRequest.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ResetPasswordRequest.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ResetPasswordRequest.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ResetPasswordRequest.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ResetPasswordRequest~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/ResetPasswordResponse.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ResetPasswordResponse.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ResetPasswordResponse.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ResetPasswordResponse.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ResetPasswordResponse.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ResetPasswordResponse.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ResetPasswordResponse.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ResetPasswordResponse.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/ResetPasswordResponse~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/RevokeTokenRequest.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/RevokeTokenRequest.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/RevokeTokenRequest.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/RevokeTokenRequest.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/RevokeTokenRequest.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/RevokeTokenRequest.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/RevokeTokenRequest.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/RevokeTokenRequest.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/RevokeTokenRequest~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/RevokeTokenResponse.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/RevokeTokenResponse.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/RevokeTokenResponse.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/RevokeTokenResponse.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/RevokeTokenResponse.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/RevokeTokenResponse.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/RevokeTokenResponse.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/RevokeTokenResponse.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/RevokeTokenResponse~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/SecureTokenRequest.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SecureTokenRequest.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SecureTokenRequest.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SecureTokenRequest.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SecureTokenRequest.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SecureTokenRequest.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SecureTokenRequest.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SecureTokenRequest.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SecureTokenRequest~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/SecureTokenResponse.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SecureTokenResponse.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SecureTokenResponse.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SecureTokenResponse.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SecureTokenResponse.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SecureTokenResponse.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SecureTokenResponse.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SecureTokenResponse.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SecureTokenResponse~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/SendVerificationTokenRequest.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SendVerificationTokenRequest.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SendVerificationTokenRequest.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SendVerificationTokenRequest.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SendVerificationTokenRequest.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SendVerificationTokenRequest.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SendVerificationTokenRequest.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SendVerificationTokenRequest.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SendVerificationTokenRequest~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/SendVerificationTokenResponse.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SendVerificationTokenResponse.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SendVerificationTokenResponse.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SendVerificationTokenResponse.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SendVerificationTokenResponse.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SendVerificationTokenResponse.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SendVerificationTokenResponse.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SendVerificationTokenResponse.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SendVerificationTokenResponse~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/SetAccountInfoRequest.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SetAccountInfoRequest.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SetAccountInfoRequest.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SetAccountInfoRequest.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SetAccountInfoRequest.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SetAccountInfoRequest.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SetAccountInfoRequest.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SetAccountInfoRequest.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SetAccountInfoRequest~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/SetAccountInfoResponse.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SetAccountInfoResponse.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SetAccountInfoResponse.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SetAccountInfoResponse.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SetAccountInfoResponse.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SetAccountInfoResponse.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SetAccountInfoResponse.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SetAccountInfoResponse.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SetAccountInfoResponse~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/SignInWithGameCenterRequest.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SignInWithGameCenterRequest.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SignInWithGameCenterRequest.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SignInWithGameCenterRequest.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SignInWithGameCenterRequest.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SignInWithGameCenterRequest.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SignInWithGameCenterRequest.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SignInWithGameCenterRequest.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SignInWithGameCenterRequest~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/SignInWithGameCenterResponse.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SignInWithGameCenterResponse.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SignInWithGameCenterResponse.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SignInWithGameCenterResponse.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SignInWithGameCenterResponse.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SignInWithGameCenterResponse.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SignInWithGameCenterResponse.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SignInWithGameCenterResponse.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SignInWithGameCenterResponse~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/SignUpNewUserRequest.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SignUpNewUserRequest.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SignUpNewUserRequest.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SignUpNewUserRequest.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SignUpNewUserRequest.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SignUpNewUserRequest.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SignUpNewUserRequest.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SignUpNewUserRequest.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SignUpNewUserRequest~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/SignUpNewUserResponse.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SignUpNewUserResponse.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SignUpNewUserResponse.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SignUpNewUserResponse.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SignUpNewUserResponse.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SignUpNewUserResponse.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SignUpNewUserResponse.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SignUpNewUserResponse.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SignUpNewUserResponse~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/VerifyAssertionRequest.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyAssertionRequest.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyAssertionRequest.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyAssertionRequest.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyAssertionRequest.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyAssertionRequest.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyAssertionRequest.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyAssertionRequest.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyAssertionRequest~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/VerifyAssertionResponse.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyAssertionResponse.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyAssertionResponse.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyAssertionResponse.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyAssertionResponse.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyAssertionResponse.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyAssertionResponse.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyAssertionResponse.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyAssertionResponse~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/VerifyCustomTokenRequest.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyCustomTokenRequest.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyCustomTokenRequest.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyCustomTokenRequest.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyCustomTokenRequest.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyCustomTokenRequest.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyCustomTokenRequest.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyCustomTokenRequest.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyCustomTokenRequest~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/VerifyCustomTokenResponse.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyCustomTokenResponse.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyCustomTokenResponse.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyCustomTokenResponse.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyCustomTokenResponse.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyCustomTokenResponse.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyCustomTokenResponse.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyCustomTokenResponse.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyCustomTokenResponse~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/VerifyPasswordRequest.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyPasswordRequest.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyPasswordRequest.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyPasswordRequest.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyPasswordRequest.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyPasswordRequest.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyPasswordRequest.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyPasswordRequest.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyPasswordRequest~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/VerifyPasswordResponse.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyPasswordResponse.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyPasswordResponse.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyPasswordResponse.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyPasswordResponse.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyPasswordResponse.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyPasswordResponse.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyPasswordResponse.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyPasswordResponse~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/VerifyPhoneNumberRequest.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyPhoneNumberRequest.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyPhoneNumberRequest.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyPhoneNumberRequest.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyPhoneNumberRequest.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyPhoneNumberRequest.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyPhoneNumberRequest.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyPhoneNumberRequest.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyPhoneNumberRequest~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/RPC/VerifyPhoneNumberResponse.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyPhoneNumberResponse.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyPhoneNumberResponse.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyPhoneNumberResponse.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyPhoneNumberResponse.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyPhoneNumberResponse.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyPhoneNumberResponse.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyPhoneNumberResponse.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyPhoneNumberResponse~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/VerifyClientRequest.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyClientRequest.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyClientRequest.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyClientRequest.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyClientRequest.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyClientRequest.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyClientRequest.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyClientRequest.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyClientRequest~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Backend/VerifyClientResponse.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyClientResponse.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyClientResponse.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyClientResponse.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyClientResponse.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyClientResponse.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyClientResponse.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyClientResponse.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/VerifyClientResponse~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Base64URLEncodedStringExtension.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/Base64URLEncodedStringExtension.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/Base64URLEncodedStringExtension.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/Base64URLEncodedStringExtension.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/Base64URLEncodedStringExtension.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/Base64URLEncodedStringExtension.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/Base64URLEncodedStringExtension.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/Base64URLEncodedStringExtension.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/Base64URLEncodedStringExtension~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/MultiFactor/MultiFactor.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactor.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactor.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactor.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactor.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactor.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactor.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactor.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactor~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/MultiFactor/MultiFactorAssertion.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactorAssertion.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactorAssertion.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactorAssertion.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactorAssertion.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactorAssertion.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactorAssertion.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactorAssertion.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactorAssertion~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/MultiFactor/MultiFactorInfo.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactorInfo.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactorInfo.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactorInfo.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactorInfo.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactorInfo.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactorInfo.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactorInfo.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactorInfo~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/MultiFactor/MultiFactorResolver.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactorResolver.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactorResolver.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactorResolver.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactorResolver.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactorResolver.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactorResolver.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactorResolver.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactorResolver~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/MultiFactor/MultiFactorSession.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactorSession.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactorSession.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactorSession.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactorSession.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactorSession.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactorSession.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactorSession.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/MultiFactorSession~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/MultiFactor/Phone/PhoneMultiFactorAssertion.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneMultiFactorAssertion.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneMultiFactorAssertion.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneMultiFactorAssertion.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneMultiFactorAssertion.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneMultiFactorAssertion.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneMultiFactorAssertion.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneMultiFactorAssertion.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneMultiFactorAssertion~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/MultiFactor/Phone/PhoneMultiFactorGenerator.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneMultiFactorGenerator.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneMultiFactorGenerator.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneMultiFactorGenerator.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneMultiFactorGenerator.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneMultiFactorGenerator.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneMultiFactorGenerator.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneMultiFactorGenerator.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneMultiFactorGenerator~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/MultiFactor/Phone/PhoneMultiFactorInfo.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneMultiFactorInfo.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneMultiFactorInfo.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneMultiFactorInfo.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneMultiFactorInfo.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneMultiFactorInfo.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneMultiFactorInfo.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneMultiFactorInfo.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/PhoneMultiFactorInfo~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/MultiFactor/TOTP/TOTPMultFactorAssertion.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TOTPMultFactorAssertion.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TOTPMultFactorAssertion.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TOTPMultFactorAssertion.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TOTPMultFactorAssertion.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TOTPMultFactorAssertion.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TOTPMultFactorAssertion.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TOTPMultFactorAssertion.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TOTPMultFactorAssertion~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/MultiFactor/TOTP/TOTPMultiFactorGenerator.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TOTPMultiFactorGenerator.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TOTPMultiFactorGenerator.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TOTPMultiFactorGenerator.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TOTPMultiFactorGenerator.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TOTPMultiFactorGenerator.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TOTPMultiFactorGenerator.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TOTPMultiFactorGenerator.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TOTPMultiFactorGenerator~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/MultiFactor/TOTP/TOTPMultiFactorInfo.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TOTPMultiFactorInfo.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TOTPMultiFactorInfo.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TOTPMultiFactorInfo.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TOTPMultiFactorInfo.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TOTPMultiFactorInfo.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TOTPMultiFactorInfo.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TOTPMultiFactorInfo.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TOTPMultiFactorInfo~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/MultiFactor/TOTP/TOTPSecret.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TOTPSecret.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TOTPSecret.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TOTPSecret.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TOTPSecret.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TOTPSecret.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TOTPSecret.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TOTPSecret.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/TOTPSecret~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Storage/AuthKeychainServices.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthKeychainServices.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthKeychainServices.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthKeychainServices.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthKeychainServices.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthKeychainServices.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthKeychainServices.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthKeychainServices.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthKeychainServices~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Storage/AuthKeychainStorage.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthKeychainStorage.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthKeychainStorage.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthKeychainStorage.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthKeychainStorage.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthKeychainStorage.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthKeychainStorage.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthKeychainStorage.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthKeychainStorage~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Storage/AuthKeychainStorageReal.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthKeychainStorageReal.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthKeychainStorageReal.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthKeychainStorageReal.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthKeychainStorageReal.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthKeychainStorageReal.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthKeychainStorageReal.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthKeychainStorageReal.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthKeychainStorageReal~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Storage/AuthUserDefaults.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthUserDefaults.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthUserDefaults.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthUserDefaults.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthUserDefaults.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthUserDefaults.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthUserDefaults.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthUserDefaults.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthUserDefaults~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/SystemService/AuthAPNSToken.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAPNSToken.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAPNSToken.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAPNSToken.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAPNSToken.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAPNSToken.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAPNSToken.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAPNSToken.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAPNSToken~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/SystemService/AuthAPNSTokenManager.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAPNSTokenManager.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAPNSTokenManager.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAPNSTokenManager.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAPNSTokenManager.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAPNSTokenManager.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAPNSTokenManager.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAPNSTokenManager.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAPNSTokenManager~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/SystemService/AuthAPNSTokenType.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAPNSTokenType.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAPNSTokenType.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAPNSTokenType.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAPNSTokenType.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAPNSTokenType.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAPNSTokenType.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAPNSTokenType.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAPNSTokenType~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/SystemService/AuthAppCredential.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAppCredential.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAppCredential.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAppCredential.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAppCredential.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAppCredential.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAppCredential.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAppCredential.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAppCredential~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/SystemService/AuthAppCredentialManager.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAppCredentialManager.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAppCredentialManager.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAppCredentialManager.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAppCredentialManager.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAppCredentialManager.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAppCredentialManager.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAppCredentialManager.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthAppCredentialManager~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/SystemService/AuthNotificationManager.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthNotificationManager.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthNotificationManager.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthNotificationManager.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthNotificationManager.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthNotificationManager.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthNotificationManager.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthNotificationManager.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthNotificationManager~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/SystemService/AuthStoredUserManager.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthStoredUserManager.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthStoredUserManager.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthStoredUserManager.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthStoredUserManager.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthStoredUserManager.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthStoredUserManager.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthStoredUserManager.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthStoredUserManager~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/SystemService/SecureTokenService.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SecureTokenService.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SecureTokenService.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SecureTokenService.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SecureTokenService.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SecureTokenService.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SecureTokenService.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SecureTokenService.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/SecureTokenService~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/User/AdditionalUserInfo.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AdditionalUserInfo.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AdditionalUserInfo.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AdditionalUserInfo.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AdditionalUserInfo.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AdditionalUserInfo.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AdditionalUserInfo.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AdditionalUserInfo.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AdditionalUserInfo~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/User/User.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/User.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/User.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/User.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/User.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/User.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/User.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/User.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/User~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/User/UserInfo.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserInfo.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserInfo.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserInfo.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserInfo.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserInfo.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserInfo.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserInfo.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserInfo~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/User/UserInfoImpl.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserInfoImpl.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserInfoImpl.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserInfoImpl.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserInfoImpl.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserInfoImpl.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserInfoImpl.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserInfoImpl.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserInfoImpl~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/User/UserMetadata.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserMetadata.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserMetadata.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserMetadata.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserMetadata.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserMetadata.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserMetadata.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserMetadata.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserMetadata~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/User/UserProfileChangeRequest.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserProfileChangeRequest.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserProfileChangeRequest.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserProfileChangeRequest.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserProfileChangeRequest.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserProfileChangeRequest.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserProfileChangeRequest.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserProfileChangeRequest.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserProfileChangeRequest~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/User/UserProfileUpdate.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserProfileUpdate.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserProfileUpdate.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserProfileUpdate.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserProfileUpdate.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserProfileUpdate.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserProfileUpdate.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserProfileUpdate.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/UserProfileUpdate~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Utilities/AuthCondition.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthCondition.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthCondition.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthCondition.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthCondition.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthCondition.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthCondition.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthCondition.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthCondition~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Utilities/AuthDefaultUIDelegate.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthDefaultUIDelegate.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthDefaultUIDelegate.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthDefaultUIDelegate.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthDefaultUIDelegate.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthDefaultUIDelegate.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthDefaultUIDelegate.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthDefaultUIDelegate.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthDefaultUIDelegate~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Utilities/AuthErrorUtils.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthErrorUtils.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthErrorUtils.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthErrorUtils.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthErrorUtils.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthErrorUtils.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthErrorUtils.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthErrorUtils.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthErrorUtils~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Utilities/AuthErrors.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthErrors.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthErrors.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthErrors.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthErrors.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthErrors.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthErrors.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthErrors.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthErrors~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Utilities/AuthInternalErrors.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthInternalErrors.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthInternalErrors.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthInternalErrors.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthInternalErrors.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthInternalErrors.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthInternalErrors.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthInternalErrors.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthInternalErrors~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Utilities/AuthLog.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthLog.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthLog.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthLog.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthLog.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthLog.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthLog.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthLog.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthLog~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Utilities/AuthRecaptchaVerifier.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthRecaptchaVerifier.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthRecaptchaVerifier.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthRecaptchaVerifier.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthRecaptchaVerifier.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthRecaptchaVerifier.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthRecaptchaVerifier.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthRecaptchaVerifier.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthRecaptchaVerifier~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Utilities/AuthUIDelegate.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthUIDelegate.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthUIDelegate.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthUIDelegate.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthUIDelegate.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthUIDelegate.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthUIDelegate.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthUIDelegate.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthUIDelegate~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Utilities/AuthURLPresenter.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthURLPresenter.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthURLPresenter.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthURLPresenter.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthURLPresenter.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthURLPresenter.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthURLPresenter.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthURLPresenter.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthURLPresenter~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Utilities/AuthWebUtils.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthWebUtils.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthWebUtils.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthWebUtils.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthWebUtils.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthWebUtils.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthWebUtils.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthWebUtils.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthWebUtils~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Utilities/AuthWebView.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthWebView.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthWebView.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthWebView.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthWebView.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthWebView.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthWebView.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthWebView.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthWebView~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Swift/Utilities/AuthWebViewController.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthWebViewController.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthWebViewController.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthWebViewController.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthWebViewController.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthWebViewController.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthWebViewController.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthWebViewController.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/AuthWebViewController~partial.swiftmodule"}}