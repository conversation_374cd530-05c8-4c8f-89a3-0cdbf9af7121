-target arm64-apple-ios12.0-simulator '-std=c99' -fobjc-arc -fmodules -gmodules '-fmodules-cache-path=/Users/<USER>/Developer/Zadrz/DerivedData/ModuleCache.noindex' '-fmodule-name=FirebaseDatabaseInternal' -fpascal-strings -O0 -DSWIFT_PACKAGE '-DDEBUG=1' '-DOBJC_OLD_DISPATCH_PROTOTYPES=1' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk -g '-fobjc-abi-version=2' -fobjc-legacy-dispatch -fprofile-instr-generate -fcoverage-mapping -I/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/GoogleUtilities/GoogleUtilities/NSData+zlib/Public -I/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/leveldb/include -I/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/GoogleUtilities/GoogleUtilities/Logger/Public -I/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/GoogleUtilities/GoogleUtilities/Environment/Public -I/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/GoogleUtilities/third_party/IsAppEncrypted/Public -I/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/GoogleUtilities/GoogleUtilities/UserDefaults/Public -I/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseCore/Sources/Public -I/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/CoreOnly/Sources -I/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAppCheck/Interop/Public -I/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator/include -I/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseDatabase/Sources/Public -I/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk -I/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabaseInternal.build/DerivedSources-normal/arm64 -I/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabaseInternal.build/DerivedSources/arm64 -I/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabaseInternal.build/DerivedSources -F/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator -iframework /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Library/Frameworks -iframework /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk/Developer/Library/Frameworks '-fmodule-map-file=/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleUtilities-NSData.modulemap' '-fmodule-map-file=/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/leveldb.modulemap' '-fmodule-map-file=/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleUtilities-Logger.modulemap' '-fmodule-map-file=/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleUtilities-Environment.modulemap' '-fmodule-map-file=/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/third-party-IsAppEncrypted.modulemap' '-fmodule-map-file=/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleUtilities-UserDefaults.modulemap' '-fmodule-map-file=/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/FirebaseCore.modulemap' '-fmodule-map-file=/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/FirebaseCoreInternal.modulemap' '-fmodule-map-file=/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/FirebaseAppCheckInterop.modulemap' -DXcode