{"": {"diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/FirebaseDatabase-master.dia", "emit-module-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/FirebaseDatabase-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/FirebaseDatabase-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/FirebaseDatabase-master.swiftdeps"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseDatabase/Swift/Sources/Codable/DataSnapshot+ReadDecodable.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/DataSnapshot+ReadDecodable.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/DataSnapshot+ReadDecodable.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/DataSnapshot+ReadDecodable.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/DataSnapshot+ReadDecodable.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/DataSnapshot+ReadDecodable.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/DataSnapshot+ReadDecodable.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/DataSnapshot+ReadDecodable.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/DataSnapshot+ReadDecodable~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseDatabase/Swift/Sources/Codable/DatabaseReference+WriteEncodable.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/DatabaseReference+WriteEncodable.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/DatabaseReference+WriteEncodable.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/DatabaseReference+WriteEncodable.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/DatabaseReference+WriteEncodable.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/DatabaseReference+WriteEncodable.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/DatabaseReference+WriteEncodable.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/DatabaseReference+WriteEncodable.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/DatabaseReference+WriteEncodable~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseDatabase/Swift/Sources/Codable/EncoderDecoder.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/EncoderDecoder.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/EncoderDecoder.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/EncoderDecoder.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/EncoderDecoder.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/EncoderDecoder.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/EncoderDecoder.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/EncoderDecoder.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/EncoderDecoder~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseDatabase/Swift/Sources/Codable/ServerTimestamp.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/ServerTimestamp.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/ServerTimestamp.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/ServerTimestamp.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/ServerTimestamp.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/ServerTimestamp.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/ServerTimestamp.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/ServerTimestamp.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/ServerTimestamp~partial.swiftmodule"}, "/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseDatabase/Swift/Sources/SPMSwiftHeaderWorkaround.swift": {"const-values": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/SPMSwiftHeaderWorkaround.swiftconstvalues", "dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/SPMSwiftHeaderWorkaround.d", "diagnostics": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/SPMSwiftHeaderWorkaround.dia", "index-unit-output-path": "/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/SPMSwiftHeaderWorkaround.o", "llvm-bc": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/SPMSwiftHeaderWorkaround.bc", "object": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/SPMSwiftHeaderWorkaround.o", "swift-dependencies": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/SPMSwiftHeaderWorkaround.swiftdeps", "swiftmodule": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseDatabase.build/Objects-normal/arm64/SPMSwiftHeaderWorkaround~partial.swiftmodule"}}