-target arm64-apple-ios12.0-simulator '-std=c99' -fmodules -gmodules '-fmodules-cache-path=/Users/<USER>/Developer/Zadrz/DerivedData/ModuleCache.noindex' '-fmodule-name=FirebaseMessaging' -fpascal-strings -O0 -DSWIFT_PACKAGE '-DDEBUG=1' '-DPB_FIELD_32BIT=1' '-DPB_NO_PACKED_STRUCTS=1' '-DPB_ENABLE_MALLOC=1' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk -g -fprofile-instr-generate -fcoverage-mapping -I/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/promises/Sources/FBLPromises/include -I/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/GoogleUtilities/GoogleUtilities/UserDefaults/Public -I/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/GoogleUtilities/GoogleUtilities/Network/Public -I/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/GoogleUtilities/GoogleUtilities/NSData+zlib/Public -I/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/GoogleUtilities/GoogleUtilities/AppDelegateSwizzler/Public -I/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/GoogleUtilities/GoogleUtilities/Logger/Public -I/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/GoogleUtilities/GoogleUtilities/Environment/Public -I/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/GoogleUtilities/third_party/IsAppEncrypted/Public -I/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/GoogleUtilities/GoogleUtilities/Reachability/Public -I/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/GoogleDataTransport/GoogleDataTransport/GDTCORLibrary/Public -I/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/nanopb/spm_headers -I/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseInstallations/Source/Library/Public -I/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseCore/Sources/Public -I/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/CoreOnly/Sources -I/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator/include -I/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseMessaging/Sources/Public -I/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk -I/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseMessaging.build/DerivedSources-normal/arm64 -I/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseMessaging.build/DerivedSources/arm64 -I/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseMessaging.build/DerivedSources -F/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator -iframework /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Library/Frameworks -iframework /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk/Developer/Library/Frameworks '-fmodule-map-file=/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleUtilities-UserDefaults.modulemap' '-fmodule-map-file=/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleUtilities-Network.modulemap' '-fmodule-map-file=/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleUtilities-NSData.modulemap' '-fmodule-map-file=/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleUtilities-AppDelegateSwizzler.modulemap' '-fmodule-map-file=/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleUtilities-Logger.modulemap' '-fmodule-map-file=/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleUtilities-Environment.modulemap' '-fmodule-map-file=/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/third-party-IsAppEncrypted.modulemap' '-fmodule-map-file=/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleUtilities-Reachability.modulemap' '-fmodule-map-file=/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleDataTransport.modulemap' '-fmodule-map-file=/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/nanopb.modulemap' '-fmodule-map-file=/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/FirebaseInstallations.modulemap' '-fmodule-map-file=/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/FirebaseCore.modulemap' '-fmodule-map-file=/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/FirebaseCoreInternal.modulemap' -DXcode