-target arm64-apple-ios12.0-simulator '-std=c99' -fobjc-arc -fmodules -gmodules '-fmodules-cache-path=/Users/<USER>/Developer/Zadrz/DerivedData/ModuleCache.noindex' '-fmodule-name=FirebaseAuthInternal' -fpascal-strings -O0 -DSWIFT_PACKAGE '-DDEBUG=1' '-DOBJC_OLD_DISPATCH_PROTOTYPES=1' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk -g '-fobjc-abi-version=2' -fobjc-legacy-dispatch -fprofile-instr-generate -fcoverage-mapping -I/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/interop-ios-for-google-sdks/RecaptchaEnterprise/RecaptchaInterop/Public -I/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator/include -I/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Public -I/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk -I/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuthInternal.build/DerivedSources-normal/arm64 -I/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuthInternal.build/DerivedSources/arm64 -I/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuthInternal.build/DerivedSources -F/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator -iframework /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Library/Frameworks -iframework /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk/Developer/Library/Frameworks '-fmodule-map-file=/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/RecaptchaInterop.modulemap' -DXcode