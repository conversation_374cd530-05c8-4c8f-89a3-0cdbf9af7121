-target arm64-apple-ios12.0-simulator '-std=gnu++14' -fmodules -fno-cxx-modules -gmodules '-fmodules-cache-path=/Users/<USER>/Developer/Zadrz/DerivedData/ModuleCache.noindex' '-fmodule-name=leveldb' '-D_LIBCPP_HARDENING_MODE=_LIBCPP_HARDENING_MODE_DEBUG' -fpascal-strings -O0 -DSWIFT_PACKAGE '-DDEBUG=1' '-DLEVELDB_IS_BIG_ENDIAN=0' '-DLEVELDB_PLATFORM_POSIX=1' '-DHAVE_FULLFSYNC=1' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk -g -fvisibility-inlines-hidden -fprofile-instr-generate -fcoverage-mapping -I/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator/include -I/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/leveldb/include -I/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/leveldb -I/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/leveldb/include -I/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/leveldb.build/Debug-iphonesimulator/leveldb.build/DerivedSources-normal/arm64 -I/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/leveldb.build/Debug-iphonesimulator/leveldb.build/DerivedSources/arm64 -I/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/leveldb.build/Debug-iphonesimulator/leveldb.build/DerivedSources -F/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator -iframework /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Library/Frameworks -iframework /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk/Developer/Library/Frameworks -DXcode