-target arm64-apple-ios12.0-simulator -fmodules -gmodules '-fmodules-cache-path=/Users/<USER>/Developer/Zadrz/DerivedData/ModuleCache.noindex' '-fmodule-name=nanopb' -fpascal-strings -O0 -DSWIFT_PACKAGE '-DDEBUG=1' '-DPB_FIELD_32BIT=1' '-DPB_NO_PACKED_STRUCTS=1' '-DPB_ENABLE_MALLOC=1' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk -g -fprofile-instr-generate -fcoverage-mapping -I/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator/include -I/Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/nanopb/spm_headers -I/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/nanopb.build/Debug-iphonesimulator/nanopb.build/DerivedSources-normal/arm64 -I/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/nanopb.build/Debug-iphonesimulator/nanopb.build/DerivedSources/arm64 -I/Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/nanopb.build/Debug-iphonesimulator/nanopb.build/DerivedSources -F/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products/Debug-iphonesimulator -iframework /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/Library/Frameworks -iframework /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk/Developer/Library/Frameworks -DXcode