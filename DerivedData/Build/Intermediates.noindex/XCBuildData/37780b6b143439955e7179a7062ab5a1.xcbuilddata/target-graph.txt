Target dependency graph (81 targets)
Target 'Zadrz' in project '<PERSON><PERSON><PERSON>'
➜ Explicit dependency on target 'FirebaseMessaging' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseDatabase' in project 'Firebase'
➜ Explicit dependency on target 'GoogleSignInSwift' in project 'GoogleSignIn'
➜ Explicit dependency on target 'FirebaseCore' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseStorage' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseAuth' in project 'Firebase'
➜ Explicit dependency on target 'GoogleSignIn' in project 'GoogleSignIn'
Target 'GoogleSignIn' in project 'GoogleSignIn'
➜ Explicit dependency on target 'GoogleSignIn' in project 'GoogleSignIn'
➜ Explicit dependency on target 'GoogleSignIn_GoogleSignIn' in project 'GoogleSignIn'
➜ Explicit dependency on target 'AppAuth' in project 'AppAuth'
➜ Explicit dependency on target 'AppCheckCore' in project 'AppCheck'
➜ Explicit dependency on target 'GTMAppAuth' in project 'GTMAppAuth'
➜ Explicit dependency on target 'GTMSessionFetcherCore' in project 'GTMSessionFetcher'
Target 'FirebaseAuth' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseAuth' in project 'Firebase'
➜ Explicit dependency on target 'Firebase_FirebaseAuth' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseAppCheckInterop' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseAuthInterop' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseAuthInternal' in project 'Firebase'
➜ Explicit dependency on target 'Firebase' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseCoreInternal' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseCore' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseCoreExtension' in project 'Firebase'
➜ Explicit dependency on target 'RecaptchaInterop' in project 'InteropForGoogle'
➜ Explicit dependency on target 'GULNSData' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GULEnvironment' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GULLogger' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GULAppDelegateSwizzler' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GTMSessionFetcherCore' in project 'GTMSessionFetcher'
Target 'FirebaseAuth' in project 'Firebase'
➜ Explicit dependency on target 'Firebase_FirebaseAuth' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseAppCheckInterop' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseAuthInterop' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseAuthInternal' in project 'Firebase'
➜ Explicit dependency on target 'Firebase' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseCoreInternal' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseCore' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseCoreExtension' in project 'Firebase'
➜ Explicit dependency on target 'RecaptchaInterop' in project 'InteropForGoogle'
➜ Explicit dependency on target 'GULNSData' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GULEnvironment' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GULLogger' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GULAppDelegateSwizzler' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GTMSessionFetcherCore' in project 'GTMSessionFetcher'
Target 'FirebaseAuthInternal' in project 'Firebase'
➜ Explicit dependency on target 'RecaptchaInterop' in project 'InteropForGoogle'
Target 'RecaptchaInterop' in project 'InteropForGoogle'
➜ Explicit dependency on target 'RecaptchaInterop' in project 'InteropForGoogle'
Target 'RecaptchaInterop' in project 'InteropForGoogle' (no dependencies)
Target 'Firebase_FirebaseAuth' in project 'Firebase' (no dependencies)
Target 'FirebaseStorage' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseStorage' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseAppCheckInterop' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseAuthInterop' in project 'Firebase'
➜ Explicit dependency on target 'Firebase' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseCoreInternal' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseCore' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseCoreExtension' in project 'Firebase'
➜ Explicit dependency on target 'GULNSData' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GULEnvironment' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GULLogger' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GTMSessionFetcherCore' in project 'GTMSessionFetcher'
Target 'FirebaseStorage' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseAppCheckInterop' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseAuthInterop' in project 'Firebase'
➜ Explicit dependency on target 'Firebase' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseCoreInternal' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseCore' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseCoreExtension' in project 'Firebase'
➜ Explicit dependency on target 'GULNSData' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GULEnvironment' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GULLogger' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GTMSessionFetcherCore' in project 'GTMSessionFetcher'
Target 'FirebaseCoreExtension' in project 'Firebase'
➜ Explicit dependency on target 'Firebase_FirebaseCoreExtension' in project 'Firebase'
Target 'Firebase_FirebaseCoreExtension' in project 'Firebase' (no dependencies)
Target 'FirebaseAuthInterop' in project 'Firebase' (no dependencies)
Target 'FirebaseCore' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseCore' in project 'Firebase'
➜ Explicit dependency on target 'Firebase_FirebaseCore' in project 'Firebase'
➜ Explicit dependency on target 'Firebase' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseCoreInternal' in project 'Firebase'
➜ Explicit dependency on target 'GULNSData' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GULEnvironment' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GULLogger' in project 'GoogleUtilities'
Target 'GoogleSignInSwift' in project 'GoogleSignIn'
➜ Explicit dependency on target 'GoogleSignInSwift' in project 'GoogleSignIn'
➜ Explicit dependency on target 'GoogleSignIn_GoogleSignInSwift' in project 'GoogleSignIn'
➜ Explicit dependency on target 'GoogleSignIn' in project 'GoogleSignIn'
➜ Explicit dependency on target 'AppAuth' in project 'AppAuth'
➜ Explicit dependency on target 'AppCheckCore' in project 'AppCheck'
➜ Explicit dependency on target 'GTMAppAuth' in project 'GTMAppAuth'
➜ Explicit dependency on target 'GTMSessionFetcherCore' in project 'GTMSessionFetcher'
Target 'GoogleSignInSwift' in project 'GoogleSignIn'
➜ Explicit dependency on target 'GoogleSignIn_GoogleSignInSwift' in project 'GoogleSignIn'
➜ Explicit dependency on target 'GoogleSignIn' in project 'GoogleSignIn'
➜ Explicit dependency on target 'AppAuth' in project 'AppAuth'
➜ Explicit dependency on target 'AppCheckCore' in project 'AppCheck'
➜ Explicit dependency on target 'GTMAppAuth' in project 'GTMAppAuth'
➜ Explicit dependency on target 'GTMSessionFetcherCore' in project 'GTMSessionFetcher'
Target 'GoogleSignIn' in project 'GoogleSignIn'
➜ Explicit dependency on target 'GoogleSignIn_GoogleSignIn' in project 'GoogleSignIn'
➜ Explicit dependency on target 'AppAuth' in project 'AppAuth'
➜ Explicit dependency on target 'AppCheckCore' in project 'AppCheck'
➜ Explicit dependency on target 'GTMAppAuth' in project 'GTMAppAuth'
➜ Explicit dependency on target 'GTMSessionFetcherCore' in project 'GTMSessionFetcher'
Target 'GTMAppAuth' in project 'GTMAppAuth'
➜ Explicit dependency on target 'GTMAppAuth' in project 'GTMAppAuth'
➜ Explicit dependency on target 'GTMAppAuth_GTMAppAuth' in project 'GTMAppAuth'
➜ Explicit dependency on target 'GTMSessionFetcherCore' in project 'GTMSessionFetcher'
➜ Explicit dependency on target 'AppAuthCore' in project 'AppAuth'
Target 'GTMAppAuth' in project 'GTMAppAuth'
➜ Explicit dependency on target 'GTMAppAuth_GTMAppAuth' in project 'GTMAppAuth'
➜ Explicit dependency on target 'GTMSessionFetcherCore' in project 'GTMSessionFetcher'
➜ Explicit dependency on target 'AppAuthCore' in project 'AppAuth'
Target 'AppAuthCore' in project 'AppAuth'
➜ Explicit dependency on target 'AppAuthCore' in project 'AppAuth'
➜ Explicit dependency on target 'AppAuth_AppAuthCore' in project 'AppAuth'
Target 'GTMSessionFetcherCore' in project 'GTMSessionFetcher'
➜ Explicit dependency on target 'GTMSessionFetcherCore' in project 'GTMSessionFetcher'
➜ Explicit dependency on target 'GTMSessionFetcher_GTMSessionFetcherCore' in project 'GTMSessionFetcher'
Target 'GTMSessionFetcherCore' in project 'GTMSessionFetcher'
➜ Explicit dependency on target 'GTMSessionFetcher_GTMSessionFetcherCore' in project 'GTMSessionFetcher'
Target 'GTMSessionFetcher_GTMSessionFetcherCore' in project 'GTMSessionFetcher' (no dependencies)
Target 'GTMAppAuth_GTMAppAuth' in project 'GTMAppAuth' (no dependencies)
Target 'AppCheckCore' in project 'AppCheck'
➜ Explicit dependency on target 'AppCheckCore' in project 'AppCheck'
➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
➜ Explicit dependency on target 'GULEnvironment' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GULUserDefaults' in project 'GoogleUtilities'
Target 'AppCheckCore' in project 'AppCheck'
➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
➜ Explicit dependency on target 'GULEnvironment' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GULUserDefaults' in project 'GoogleUtilities'
Target 'AppAuth' in project 'AppAuth'
➜ Explicit dependency on target 'AppAuth' in project 'AppAuth'
➜ Explicit dependency on target 'AppAuth_AppAuth' in project 'AppAuth'
➜ Explicit dependency on target 'AppAuthCore' in project 'AppAuth'
Target 'AppAuth' in project 'AppAuth'
➜ Explicit dependency on target 'AppAuth_AppAuth' in project 'AppAuth'
➜ Explicit dependency on target 'AppAuthCore' in project 'AppAuth'
Target 'AppAuthCore' in project 'AppAuth'
➜ Explicit dependency on target 'AppAuth_AppAuthCore' in project 'AppAuth'
Target 'AppAuth_AppAuthCore' in project 'AppAuth' (no dependencies)
Target 'AppAuth_AppAuth' in project 'AppAuth' (no dependencies)
Target 'GoogleSignIn_GoogleSignIn' in project 'GoogleSignIn' (no dependencies)
Target 'GoogleSignIn_GoogleSignInSwift' in project 'GoogleSignIn' (no dependencies)
Target 'FirebaseDatabase' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseDatabase' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseAppCheckInterop' in project 'Firebase'
➜ Explicit dependency on target 'Firebase' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseCoreInternal' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseCore' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseDatabaseInternal' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseSharedSwift' in project 'Firebase'
➜ Explicit dependency on target 'GULNSData' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GULEnvironment' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GULLogger' in project 'GoogleUtilities'
➜ Explicit dependency on target 'leveldb' in project 'leveldb'
➜ Explicit dependency on target 'GULUserDefaults' in project 'GoogleUtilities'
Target 'FirebaseDatabase' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseAppCheckInterop' in project 'Firebase'
➜ Explicit dependency on target 'Firebase' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseCoreInternal' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseCore' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseDatabaseInternal' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseSharedSwift' in project 'Firebase'
➜ Explicit dependency on target 'GULNSData' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GULEnvironment' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GULLogger' in project 'GoogleUtilities'
➜ Explicit dependency on target 'leveldb' in project 'leveldb'
➜ Explicit dependency on target 'GULUserDefaults' in project 'GoogleUtilities'
Target 'FirebaseSharedSwift' in project 'Firebase' (no dependencies)
Target 'FirebaseDatabaseInternal' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseAppCheckInterop' in project 'Firebase'
➜ Explicit dependency on target 'Firebase' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseCoreInternal' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseCore' in project 'Firebase'
➜ Explicit dependency on target 'GULNSData' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GULEnvironment' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GULLogger' in project 'GoogleUtilities'
➜ Explicit dependency on target 'leveldb' in project 'leveldb'
➜ Explicit dependency on target 'GULUserDefaults' in project 'GoogleUtilities'
Target 'leveldb' in project 'leveldb'
➜ Explicit dependency on target 'leveldb' in project 'leveldb'
➜ Explicit dependency on target 'leveldb_leveldb' in project 'leveldb'
Target 'leveldb' in project 'leveldb'
➜ Explicit dependency on target 'leveldb_leveldb' in project 'leveldb'
Target 'leveldb_leveldb' in project 'leveldb' (no dependencies)
Target 'FirebaseAppCheckInterop' in project 'Firebase' (no dependencies)
Target 'FirebaseMessaging' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseMessaging' in project 'Firebase'
➜ Explicit dependency on target 'Firebase_FirebaseMessaging' in project 'Firebase'
➜ Explicit dependency on target 'Firebase' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseCoreInternal' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseCore' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseInstallations' in project 'Firebase'
➜ Explicit dependency on target 'GULNSData' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GULEnvironment' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GULLogger' in project 'GoogleUtilities'
➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
➜ Explicit dependency on target 'GULUserDefaults' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GULAppDelegateSwizzler' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GULReachability' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleDataTransport' in project 'GoogleDataTransport'
➜ Explicit dependency on target 'nanopb' in project 'nanopb'
Target 'FirebaseMessaging' in project 'Firebase'
➜ Explicit dependency on target 'Firebase_FirebaseMessaging' in project 'Firebase'
➜ Explicit dependency on target 'Firebase' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseCoreInternal' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseCore' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseInstallations' in project 'Firebase'
➜ Explicit dependency on target 'GULNSData' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GULEnvironment' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GULLogger' in project 'GoogleUtilities'
➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
➜ Explicit dependency on target 'GULUserDefaults' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GULAppDelegateSwizzler' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GULReachability' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleDataTransport' in project 'GoogleDataTransport'
➜ Explicit dependency on target 'nanopb' in project 'nanopb'
Target 'GoogleDataTransport' in project 'GoogleDataTransport'
➜ Explicit dependency on target 'GoogleDataTransport' in project 'GoogleDataTransport'
➜ Explicit dependency on target 'GoogleDataTransport_GoogleDataTransport' in project 'GoogleDataTransport'
➜ Explicit dependency on target 'nanopb' in project 'nanopb'
➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
Target 'GoogleDataTransport' in project 'GoogleDataTransport'
➜ Explicit dependency on target 'GoogleDataTransport_GoogleDataTransport' in project 'GoogleDataTransport'
➜ Explicit dependency on target 'nanopb' in project 'nanopb'
➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
Target 'nanopb' in project 'nanopb'
➜ Explicit dependency on target 'nanopb' in project 'nanopb'
➜ Explicit dependency on target 'nanopb_nanopb' in project 'nanopb'
Target 'nanopb' in project 'nanopb'
➜ Explicit dependency on target 'nanopb_nanopb' in project 'nanopb'
Target 'nanopb_nanopb' in project 'nanopb' (no dependencies)
Target 'GoogleDataTransport_GoogleDataTransport' in project 'GoogleDataTransport' (no dependencies)
Target 'GULReachability' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities-Reachability' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-Reachability' in project 'GoogleUtilities'
➜ Explicit dependency on target 'third-party-IsAppEncrypted' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities-Environment' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities-Logger' in project 'GoogleUtilities'
Target 'GULAppDelegateSwizzler' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities-AppDelegateSwizzler' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-AppDelegateSwizzler' in project 'GoogleUtilities'
➜ Explicit dependency on target 'third-party-IsAppEncrypted' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities-Environment' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities-Logger' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities-NSData' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities-Reachability' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities-Network' in project 'GoogleUtilities'
Target 'GoogleUtilities-AppDelegateSwizzler' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-AppDelegateSwizzler' in project 'GoogleUtilities'
➜ Explicit dependency on target 'third-party-IsAppEncrypted' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities-Environment' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities-Logger' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities-NSData' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities-Reachability' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities-Network' in project 'GoogleUtilities'
Target 'GoogleUtilities-Network' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-Network' in project 'GoogleUtilities'
➜ Explicit dependency on target 'third-party-IsAppEncrypted' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities-Environment' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities-Logger' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities-NSData' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities-Reachability' in project 'GoogleUtilities'
Target 'GoogleUtilities_GoogleUtilities-Network' in project 'GoogleUtilities' (no dependencies)
Target 'GoogleUtilities-Reachability' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-Reachability' in project 'GoogleUtilities'
➜ Explicit dependency on target 'third-party-IsAppEncrypted' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities-Environment' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities-Logger' in project 'GoogleUtilities'
Target 'GoogleUtilities_GoogleUtilities-Reachability' in project 'GoogleUtilities' (no dependencies)
Target 'GoogleUtilities_GoogleUtilities-AppDelegateSwizzler' in project 'GoogleUtilities' (no dependencies)
Target 'FirebaseInstallations' in project 'Firebase'
➜ Explicit dependency on target 'Firebase_FirebaseInstallations' in project 'Firebase'
➜ Explicit dependency on target 'Firebase' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseCoreInternal' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseCore' in project 'Firebase'
➜ Explicit dependency on target 'GULNSData' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GULEnvironment' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GULLogger' in project 'GoogleUtilities'
➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
➜ Explicit dependency on target 'GULUserDefaults' in project 'GoogleUtilities'
Target 'GULUserDefaults' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities-UserDefaults' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-UserDefaults' in project 'GoogleUtilities'
➜ Explicit dependency on target 'third-party-IsAppEncrypted' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities-Environment' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities-Logger' in project 'GoogleUtilities'
Target 'GoogleUtilities-UserDefaults' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-UserDefaults' in project 'GoogleUtilities'
➜ Explicit dependency on target 'third-party-IsAppEncrypted' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities-Environment' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities-Logger' in project 'GoogleUtilities'
Target 'GoogleUtilities_GoogleUtilities-UserDefaults' in project 'GoogleUtilities' (no dependencies)
Target 'FBLPromises' in project 'Promises'
➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
➜ Explicit dependency on target 'Promises_FBLPromises' in project 'Promises'
Target 'FBLPromises' in project 'Promises'
➜ Explicit dependency on target 'Promises_FBLPromises' in project 'Promises'
Target 'Promises_FBLPromises' in project 'Promises' (no dependencies)
Target 'Firebase_FirebaseInstallations' in project 'Firebase' (no dependencies)
Target 'FirebaseCore' in project 'Firebase'
➜ Explicit dependency on target 'Firebase_FirebaseCore' in project 'Firebase'
➜ Explicit dependency on target 'Firebase' in project 'Firebase'
➜ Explicit dependency on target 'FirebaseCoreInternal' in project 'Firebase'
➜ Explicit dependency on target 'GULNSData' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GULEnvironment' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GULLogger' in project 'GoogleUtilities'
Target 'GULLogger' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities-Logger' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-Logger' in project 'GoogleUtilities'
➜ Explicit dependency on target 'third-party-IsAppEncrypted' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities-Environment' in project 'GoogleUtilities'
Target 'GoogleUtilities-Logger' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-Logger' in project 'GoogleUtilities'
➜ Explicit dependency on target 'third-party-IsAppEncrypted' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities-Environment' in project 'GoogleUtilities'
Target 'GoogleUtilities_GoogleUtilities-Logger' in project 'GoogleUtilities' (no dependencies)
Target 'GULEnvironment' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities-Environment' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-Environment' in project 'GoogleUtilities'
➜ Explicit dependency on target 'third-party-IsAppEncrypted' in project 'GoogleUtilities'
Target 'GoogleUtilities-Environment' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-Environment' in project 'GoogleUtilities'
➜ Explicit dependency on target 'third-party-IsAppEncrypted' in project 'GoogleUtilities'
Target 'third-party-IsAppEncrypted' in project 'GoogleUtilities' (no dependencies)
Target 'GoogleUtilities_GoogleUtilities-Environment' in project 'GoogleUtilities' (no dependencies)
Target 'Firebase_FirebaseCore' in project 'Firebase' (no dependencies)
Target 'FirebaseCoreInternal' in project 'Firebase'
➜ Explicit dependency on target 'Firebase_FirebaseCoreInternal' in project 'Firebase'
➜ Explicit dependency on target 'GULNSData' in project 'GoogleUtilities'
Target 'GULNSData' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities-NSData' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-NSData' in project 'GoogleUtilities'
Target 'GoogleUtilities-NSData' in project 'GoogleUtilities'
➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-NSData' in project 'GoogleUtilities'
Target 'GoogleUtilities_GoogleUtilities-NSData' in project 'GoogleUtilities' (no dependencies)
Target 'Firebase_FirebaseCoreInternal' in project 'Firebase' (no dependencies)
Target 'Firebase' in project 'Firebase' (no dependencies)
Target 'Firebase_FirebaseMessaging' in project 'Firebase' (no dependencies)