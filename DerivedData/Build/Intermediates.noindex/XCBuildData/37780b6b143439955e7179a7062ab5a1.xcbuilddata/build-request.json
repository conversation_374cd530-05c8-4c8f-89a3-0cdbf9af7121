{"buildCommand": {"command": "build", "skipDependencies": false, "style": "buildOnly"}, "configuredTargets": [{"guid": "6b1b20bb7a3f82b41278d89c4d94b2d14d09f03348a5be4fdaa84215596df2eb"}], "containerPath": "/Users/<USER>/Developer/Zadrz/Zadrz.xcodeproj", "continueBuildingAfterErrors": false, "dependencyScope": "workspace", "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "build", "activeArchitecture": "arm64", "activeRunDestination": {"disableOnlyActiveArch": false, "platform": "iphonesimulator", "sdk": "iphonesimulator18.5", "sdkVariant": "iphonesimulator", "supportedArchitectures": ["arm64", "x86_64"], "targetArchitecture": "arm64"}, "arenaInfo": {"buildIntermediatesPath": "/Users/<USER>/Dev<PERSON>per/<PERSON><PERSON>/DerivedData/Build/Intermediates.noindex", "buildProductsPath": "/Users/<USER>/Developer/Zadrz/DerivedData/Build/Products", "derivedDataPath": "/Users/<USER>/<PERSON><PERSON><PERSON>/<PERSON><PERSON>/DerivedData", "indexDataStoreFolderPath": "/Users/<USER>/Developer/Zadrz/DerivedData/Index.noindex/DataStore", "indexEnableDataStore": true, "indexPCHPath": "/Users/<USER>/Dev<PERSON>per/Z<PERSON>rz/DerivedData/Index.noindex/PrecompiledHeaders", "pchPath": "/Users/<USER>/Developer/Zad<PERSON>/DerivedData/Build/Intermediates.noindex/PrecompiledHeaders"}, "configurationName": "Debug", "overrides": {"commandLine": {"table": {}}, "synthesized": {"table": {"ACTION": "build", "ASSETCATALOG_FILTER_FOR_DEVICE_MODEL": "iPhone17,3", "ASSETCATALOG_FILTER_FOR_DEVICE_OS_VERSION": "18.5", "ASSETCATALOG_FILTER_FOR_THINNING_DEVICE_CONFIGURATION": "iPhone17,3", "BUILD_ACTIVE_RESOURCES_ONLY": "YES", "CLANG_COVERAGE_MAPPING": "YES", "CLANG_PROFILE_DATA_DIRECTORY": "/Users/<USER>/Developer/<PERSON>adrz/DerivedData/Build/ProfileData", "COLOR_DIAGNOSTICS": "YES", "diagnostic_message_length": "162", "ENABLE_PREVIEWS": "NO", "ENABLE_XOJIT_PREVIEWS": "YES", "ONLY_ACTIVE_ARCH": "YES", "TARGET_DEVICE_IDENTIFIER": "B45E97C8-F1DC-4361-8AF4-40B597E9ABFF", "TARGET_DEVICE_MODEL": "iPhone17,3", "TARGET_DEVICE_OS_VERSION": "18.5", "TARGET_DEVICE_PLATFORM_NAME": "iphonesimulator"}}}}, "schemeCommand": "launch", "showNonLoggedProgress": true, "useDryRun": false, "useImplicitDependencies": true, "useLegacyBuildLocations": false, "useParallelTargets": true}