{"buildConfigurations": [{"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_TEAM": "V992GDVW43", "ENABLE_PREVIEWS": "YES", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_FILE": "Zadrz/Info.plist", "INFOPLIST_KEY_UIApplicationSceneManifest_Generation": "YES", "INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents": "YES", "INFOPLIST_KEY_UILaunchScreen_Generation": "YES", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad": "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone": "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.alijaver.Zadrz", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "6b1b20bb7a3f82b41278d89c4d94b2d10c175f2ec94ed3f6b1fb146b308837b2", "name": "Debug"}, {"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_TEAM": "V992GDVW43", "ENABLE_PREVIEWS": "YES", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_FILE": "Zadrz/Info.plist", "INFOPLIST_KEY_UIApplicationSceneManifest_Generation": "YES", "INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents": "YES", "INFOPLIST_KEY_UILaunchScreen_Generation": "YES", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad": "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone": "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.alijaver.Zadrz", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "6b1b20bb7a3f82b41278d89c4d94b2d10ff04968784ecfeec420a8c15842ef55", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d11c9448e8c4305b4184682e75e5568e69", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1ff1a37669040479992f6e94c08b7a200"}, {"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d1f19cca45759a27ae76cc61d256f831c4", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1404a56133db24da3f014b233302100cb"}, {"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d1a9db4d77176df01cad0964b6d05bd960", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1c5536493e55b252efe42679301bbe439"}, {"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d19e8e715ee7cf984cd94f127a4b9af66f", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1e59efb355fa1c3c724bb4068c2326ca9"}, {"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d13183d6ffdcbca49bd7f816ddad65a020", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1160abe0a5b6661974cef08e355c4fc29"}, {"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d168bb11b1af96e2aba7fa6ff09ae41a48", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d171c1292712c6516aa9358003c5ba9005"}, {"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d18202bd1e921c22dd1f88525dc9e7088e", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d16b2e09829012749088ed956f8c1f2d60"}, {"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d1d710581760135981fe2b63f14d1163d0", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1523625b111acbeb3060222da384bb5f0"}, {"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d18cb9710d578309e4790ea069c0a601d2", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d13812a5ea0865d6f462619693646d5b38"}, {"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d1a8f6ecb0fb74fe334ac71939f301a171", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1c11d2de6e7af84f4aaaabda2a428faf7"}, {"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d14b7c2145124710cfa9a432b45ab92ba4", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1a0cda9915ada6e60bb078cb49084e570"}, {"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d1df5e11197cb223e21b0107a085941414", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1ab84399f34d64c0a3a6f2107ac71f334"}, {"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d108d06d49113428ef039c6792ee4c0416", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d10d8c5f8e19b8244b069b5b06cdcae6ff"}, {"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d17373c8f60dd278789df48f8ff1845fa1", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1b82bece94e48f636010ae5decc82d227"}, {"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d1e568725081015bdce248252ca321e2e4", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d13c5290c8bbe9df513ea40dd5826bbd78"}, {"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d1ed6262e339bbbc967230e6bdeb24dbcd", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d198cf4134caf4f311a560267cd3481afa"}, {"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d18d110566600a2032ed78fde4e7a5a138", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d173508c093c171313ba765058685dd93f"}, {"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d16c42d1baf67274d227e9c3ef053b3ca1", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d188f35f6459a0a019565da2e1166d4478"}, {"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d17efa69a3c4d2fb88f6903c12986d1c7e", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1d2cbf8c1346ff6c037cf57e9bfc44a40"}, {"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d13f0ee5092ff33e411a72fbd35e9ced0f", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1943cde8044e36dd462bc129cdbeb2f04"}, {"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d1da7399073ceae920969c65d52bf4fa61", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1c925f51ddb360e960c681f97e79d7453"}, {"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d17048e9fa4f8c0036ee494119a42d3a41", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d16b05072c1ac4ca5ddd9e45e947adc533"}, {"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d1d954ab76b31ff37e8b41cfabcaadaeb1", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d11eb478164b4cf0dfd3b50efd23fd6fce"}, {"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d128758ef9ca821b1d4f25fc6413f7b1ea", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1f705558213984c3696c2d308ab834000"}, {"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d10659a58e08eba994a817280805f9c2fa", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d13e5dc3749d6d6c89aa49f2cccd15d4be"}, {"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d1602c497117cba15d8396c3d20ce6d41f", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d122c0d050a956fe20516f3176a886b72f"}, {"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d183efc8e3b334aea57433a950830980f8", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1ba9a01fd313011e17b9b648d2d13b0e6"}, {"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d165de3ee40d5ed17f8b74a1d84bf613cc", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1d8ee530548476269991758d623792eed"}, {"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d13abaab69b95b850e0bc2583f1106950c", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d14f12bc988ccbfda1a270f94499525a42"}, {"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d11371f52eab78cdeb4aef2babd9278ec4", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d16ca2dbfd56ce1e82bbdc1b26fd630160"}, {"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d1ffd11b731c58e390fdcab57ba85fa35e", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1556776457760bd80cd995ee15347f6df"}, {"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d1eedccdcf0d96084d57dc376272627ad0", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d10ebbc2290f552114809022dd81bae443"}, {"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d1d0f998bb27129f6ed84f77c6dc10faeb", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1b604e9d058ab3b8e47fd2406eb87a5ae"}, {"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d1f8051fcfb02037fe4575a8ef5666aa3b", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d183d1659c533c384dcdb6f44ec902fbef"}, {"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d1175b36b84b87742de18ff6b53ab5c3e9", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1e16682758e2949022cab88b43e860548"}, {"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d12efc8f81010d3ebe29731972b5279445", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1f39cdce4a5dec3baabd11801aff99962"}], "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1e82f17974a11cfac7253ebdb8e420688", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"guid": "836bd2c2a92d137b1c7de14873557d66", "targetReference": "PACKAGE-PRODUCT:FirebaseMessaging"}, {"guid": "61e564ca94eb59e2f11867794a2ba449", "targetReference": "PACKAGE-PRODUCT:FirebaseDatabase"}, {"guid": "26af37c7712cdf4ccb490b804532c13e", "targetReference": "PACKAGE-PRODUCT:GoogleSignInSwift"}, {"guid": "7c5c3c2310c9090d1c69811780a16615", "targetReference": "PACKAGE-PRODUCT:FirebaseCore"}, {"guid": "f04962da4a10153d3d3e97da304e6bfe", "targetReference": "PACKAGE-PRODUCT:FirebaseStorage"}, {"guid": "94c5b52f2f034ca9a8f90b757c4de305", "targetReference": "PACKAGE-PRODUCT:FirebaseAuth"}, {"guid": "e0bf0cdaf1abda973e50e901098843cd", "targetReference": "PACKAGE-PRODUCT:GoogleSignIn"}], "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1516495a99465522392324e6b97a7de11", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d1e92475168f3f24c2d042541e89023ee3", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d17dfdd13c3dcc829df9ce42116b2caf4e"}, {"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d1c92bdd04067589c758530f96165d00fc", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1cc63717c3eccde36c23819405187f6c5"}, {"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d15c79e79d2a6b0eac144ddd592aa4de9a", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1091c963ea44a8af75cb588c9b55234a7"}, {"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d1c38a8dc09873bf9ae1f94cb54013a85e", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1bc90cd561bc0c32215665e87c84c8173"}, {"fileReference": "6b1b20bb7a3f82b41278d89c4d94b2d17ef72b6ce09de3a5a8cc7a4ca888b4b4", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d175867081da93705154c2a6172f8fb7c9"}], "guid": "6b1b20bb7a3f82b41278d89c4d94b2d13ed9a0461791b5106f614804eafe644c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "PACKAGE-PRODUCT:FirebaseMessaging", "name": "FirebaseMessaging"}, {"guid": "PACKAGE-PRODUCT:FirebaseDatabase", "name": "FirebaseDatabase"}, {"guid": "PACKAGE-PRODUCT:GoogleSignInSwift", "name": "GoogleSignInSwift"}, {"guid": "PACKAGE-PRODUCT:FirebaseCore", "name": "FirebaseCore"}, {"guid": "PACKAGE-PRODUCT:FirebaseStorage", "name": "FirebaseStorage"}, {"guid": "PACKAGE-PRODUCT:FirebaseAuth", "name": "FirebaseAuth"}, {"guid": "PACKAGE-PRODUCT:GoogleSignIn", "name": "GoogleSignIn"}], "guid": "6b1b20bb7a3f82b41278d89c4d94b2d14d09f03348a5be4fdaa84215596df2eb", "name": "Zadrz", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "6b1b20bb7a3f82b41278d89c4d94b2d1483a5a4fb3aafb144e2b72a00a2ef859", "name": "Zadrz.app", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.application", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}