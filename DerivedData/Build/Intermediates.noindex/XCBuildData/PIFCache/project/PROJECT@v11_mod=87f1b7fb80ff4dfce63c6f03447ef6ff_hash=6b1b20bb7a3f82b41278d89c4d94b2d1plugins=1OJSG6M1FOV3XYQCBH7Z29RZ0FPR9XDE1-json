{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "DEVELOPMENT_TEAM": "V992GDVW43", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.5", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "SDKROOT": "iphoneos", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG $(inherited)", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "6b1b20bb7a3f82b41278d89c4d94b2d182f4cf629736d9faa7f442bbd1c55d1e", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEVELOPMENT_TEAM": "V992GDVW43", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.5", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "SDKROOT": "iphoneos", "SWIFT_COMPILATION_MODE": "wholemodule", "VALIDATE_PRODUCT": "YES"}, "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1deacbcb96dbc1697bc67d5617444a612", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d16c42d1baf67274d227e9c3ef053b3ca1", "path": "AppRootView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d183efc8e3b334aea57433a950830980f8", "path": "MainTabView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1bd576fce8bc53e4e343944d674011a91", "name": "App", "path": "App", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d10659a58e08eba994a817280805f9c2fa", "path": "View+Extensions.swift", "sourceTree": "<group>", "type": "file"}], "guid": "6b1b20bb7a3f82b41278d89c4d94b2d172ef54b8d87f6eb8a4294816fcbb13af", "name": "Extensions", "path": "Extensions", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d108d06d49113428ef039c6792ee4c0416", "path": "ChatModels.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d12efc8f81010d3ebe29731972b5279445", "path": "UserModel.swift", "sourceTree": "<group>", "type": "file"}], "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1765c5dd8dc787a39645b5de7973f8381", "name": "Models", "path": "Models", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d168bb11b1af96e2aba7fa6ff09ae41a48", "path": "NavigationRouter.swift", "sourceTree": "<group>", "type": "file"}], "guid": "6b1b20bb7a3f82b41278d89c4d94b2d18ba11192f0bb39449441e463ea9cdd4a", "name": "Navigation", "path": "Navigation", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1ed6262e339bbbc967230e6bdeb24dbcd", "path": "AuthenticationService.swift", "sourceTree": "<group>", "type": "file"}], "guid": "6b1b20bb7a3f82b41278d89c4d94b2d16a8a71befb9bb51472a684e5480705c4", "name": "Services", "path": "Services", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d11c9448e8c4305b4184682e75e5568e69", "path": "Constants.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d18202bd1e921c22dd1f88525dc9e7088e", "path": "PerformanceOptimizations.swift", "sourceTree": "<group>", "type": "file"}], "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1f4988139ce50f207a194b92ec66277b9", "name": "Utilities", "path": "Utilities", "sourceTree": "<group>", "type": "group"}], "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1ec623e4da1a84347d6033a38ad77d285", "name": "Core", "path": "Core", "sourceTree": "<group>", "type": "group"}, {"children": [{"guid": "6b1b20bb7a3f82b41278d89c4d94b2d1e7f01afa0ef7207d3cd7e262d47fa22e", "name": "<PERSON><PERSON>", "path": "<PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d17373c8f60dd278789df48f8ff1845fa1", "path": "AuthenticationViewModel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d17efa69a3c4d2fb88f6903c12986d1c7e", "path": "ForgotPasswordView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d165de3ee40d5ed17f8b74a1d84bf613cc", "path": "GreetingView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d11371f52eab78cdeb4aef2babd9278ec4", "path": "LoginView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1d0f998bb27129f6ed84f77c6dc10faeb", "path": "SignUpView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1a42cce42de2f97aca7cbbf898238c86b", "name": "Authentication", "path": "Authentication", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d19e8e715ee7cf984cd94f127a4b9af66f", "path": "ChatDetailView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d128758ef9ca821b1d4f25fc6413f7b1ea", "path": "ChatInputBar.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d18cb9710d578309e4790ea069c0a601d2", "path": "ChatListView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1e568725081015bdce248252ca321e2e4", "path": "ChatRowView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d13f0ee5092ff33e411a72fbd35e9ced0f", "path": "ChatsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1f19cca45759a27ae76cc61d256f831c4", "path": "MessagesScrollView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d18d110566600a2032ed78fde4e7a5a138", "path": "MessageTypeViews.swift", "sourceTree": "<group>", "type": "file"}], "guid": "6b1b20bb7a3f82b41278d89c4d94b2d112ef30830cefaea0c27573feb7eee652", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}], "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1bba5f47163614f3627637cf4e6021d5a", "name": "Cha<PERSON>", "path": "Cha<PERSON>", "sourceTree": "<group>", "type": "group"}, {"guid": "6b1b20bb7a3f82b41278d89c4d94b2d13c4dc97aa8f3bc0a1d8537fcb3ac3c0a", "name": "Main", "path": "Main", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1a9db4d77176df01cad0964b6d05bd960", "path": "ConversationStyleView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1d710581760135981fe2b63f14d1163d0", "path": "InterestsSelectionView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1c14632de9d168c4cfd5d53333a326650", "name": "Onboarding", "path": "Onboarding", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1d954ab76b31ff37e8b41cfabcaadaeb1", "path": "DeleteAccountView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1df5e11197cb223e21b0107a085941414", "path": "EditProfileView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1175b36b84b87742de18ff6b53ab5c3e9", "path": "ProfileHeaderSection.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d13abaab69b95b850e0bc2583f1106950c", "path": "ProfileOptionsSection.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1ffd11b731c58e390fdcab57ba85fa35e", "path": "ProfileView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1a8f6ecb0fb74fe334ac71939f301a171", "path": "UpdatePasswordView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1624ac3b0c0ef496985eb7f958600950f", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1f8051fcfb02037fe4575a8ef5666aa3b", "path": "AvatarSelectionView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1b455f849bd59ba8bf15ff725365a0c2a", "name": "Profile", "path": "Profile", "sourceTree": "<group>", "type": "group"}], "guid": "6b1b20bb7a3f82b41278d89c4d94b2d182d1aaa38392c65010677e454d3023a9", "name": "Features", "path": "Features", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "folder.assetcatalog", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d17ef72b6ce09de3a5a8cc7a4ca888b4b4", "path": "Assets.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d15c79e79d2a6b0eac144ddd592aa4de9a", "path": "GoogleService-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "6b1b20bb7a3f82b41278d89c4d94b2d13172ae8b6159492727c314ffd44f06b3", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1da7399073ceae920969c65d52bf4fa61", "path": "CapsuleButton.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d14b7c2145124710cfa9a432b45ab92ba4", "path": "FlexibleTagLayout.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d13183d6ffdcbca49bd7f816ddad65a020", "path": "FormTextField.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1eedccdcf0d96084d57dc376272627ad0", "path": "PrimaryButton.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1602c497117cba15d8396c3d20ce6d41f", "path": "SelectionView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1e4942a4cbd089de366aa97f8e220229a", "name": "Components", "path": "Components", "sourceTree": "<group>", "type": "group"}, {"children": [{"guid": "6b1b20bb7a3f82b41278d89c4d94b2d1cdafaac665d6c7dfb82ee335301e97bc", "name": "Components", "path": "Components", "sourceTree": "<group>", "type": "group"}], "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1f2a4928c74cf24f157d3f21c1331544d", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}], "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1e9b4b5d5269805bdd7640eb7d579c79d", "name": "Shared", "path": "Shared", "sourceTree": "<group>", "type": "group"}, {"guid": "6b1b20bb7a3f82b41278d89c4d94b2d1bfc68a9aa3a97c5c6e1b08fe96357a7d", "name": "Tests", "path": "Tests", "sourceTree": "<group>", "type": "group"}, {"fileType": "text.plist.xml", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1408bed169dd857a7f7ddc5aa41e1732c", "path": "Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1c92bdd04067589c758530f96165d00fc", "path": "README.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1e92475168f3f24c2d042541e89023ee3", "path": "REFACTOR_SUMMARY.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1c38a8dc09873bf9ae1f94cb54013a85e", "path": "SWIFT_TESTING_GUIDE.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d17048e9fa4f8c0036ee494119a42d3a41", "path": "ZadrzApp.swift", "sourceTree": "<group>", "type": "file"}], "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1ce9247c69bbc8476887c6e97683c4fab", "name": "Zadrz", "path": "Zadrz", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1d86cc70ba40cc5c89e9026ba8630fd8d", "path": "ZadrzTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1449831db8b13dd92830f0aa736caf8f8", "name": "ZadrzTests", "path": "ZadrzTests", "sourceTree": "<group>", "type": "group"}, {"guid": "6b1b20bb7a3f82b41278d89c4d94b2d110df30d909df1334a0bee2900184f5a1", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1bd5567a16b8b892d41e709af2a1f2796", "name": "Zadrz", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "6b1b20bb7a3f82b41278d89c4d94b2d1", "path": "/Users/<USER>/Developer/Zadrz/Zadrz.xcodeproj", "projectDirectory": "/Users/<USER>/<PERSON><PERSON><PERSON>/<PERSON><PERSON>", "targets": ["TARGET@v11_hash=2ba5d2eaebe53d2142c30b30b39b8d23", "TARGET@v11_hash=1aac913cecd5bf64b5bab99e5d6aea5e"]}