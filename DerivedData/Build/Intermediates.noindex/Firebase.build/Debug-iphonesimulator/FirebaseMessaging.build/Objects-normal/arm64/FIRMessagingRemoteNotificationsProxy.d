dependencies: \
  /Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseMessaging/Sources/FIRMessagingRemoteNotificationsProxy.m \
  /Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseMessaging.build/DerivedSources/resource_bundle_accessor.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/System/Library/Frameworks/Foundation.framework/Modules/module.modulemap \
  /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator.sdk/usr/include/ObjectiveC.modulemap \
  /Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseMessaging/Sources/FIRMessagingRemoteNotificationsProxy.h \
  /Users/<USER>/Developer/Zadrz/DerivedData/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleUtilities-AppDelegateSwizzler.modulemap \
  /Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseMessaging/Sources/FIRMessagingConstants.h \
  /Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseMessaging/Sources/FIRMessagingLogger.h \
  /Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseMessaging/Sources/FIRMessagingCode.h \
  /Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseMessaging/Sources/FIRMessagingUtilities.h \
  /Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseMessaging/Sources/FIRMessaging_Private.h \
  /Users/<USER>/Developer/Zadrz/DerivedData/SourcePackages/checkouts/firebase-ios-sdk/FirebaseMessaging/Sources/Public/FirebaseMessaging/FIRMessaging.h
