//
//  UserModelTests.swift
//  ZadrzTests
//
//  Created by <PERSON><PERSON><PERSON> on 6/22/25.
//

import Testing
import Foundation
@testable import Zadrz

struct UserModelTests {
    
    @Test("UserModel initialization with valid data")
    func testUserModelInitialization() async throws {
        // Given
        let id = "test-user-id"
        let fullName = "John Doe"
        let email = "<EMAIL>"

        // When
        let user = UserModel(id: id, fullName: fullName, email: email)

        // Then
        #expect(user.id == id)
        #expect(user.fullName == fullName)
        #expect(user.email == email)
        #expect(user.bio == "Hey there! I am using Zadrz.")
        #expect(user.profileImageUrl == nil)
        #expect(user.emailVerified == true)
    }
    
    @Test("UserModel initialization with custom bio and profile image")
    func testUserModelInitializationWithCustomData() async throws {
        // Given
        let id = "test-user-id"
        let fullName = "<PERSON>"
        let email = "<EMAIL>"
        let bio = "iOS Developer"
        let profileImageUrl = "https://example.com/profile.jpg"
        
        // When
        let user = UserModel(
            id: id,
            fullName: fullName,
            email: email,
            bio: bio,
            profileImageUrl: profileImageUrl,
            emailVerified: true
        )
        
        // Then
        #expect(user.id == id)
        #expect(user.fullName == fullName)
        #expect(user.email == email)
        #expect(user.bio == bio)
        #expect(user.profileImageUrl == profileImageUrl)
        #expect(user.emailVerified == true)
    }
    
    @Test("UserModel initials computation")
    func testUserModelInitials() async throws {
        // Given
        let user1 = UserModel(id: "1", fullName: "John Doe", email: "<EMAIL>")
        let user2 = UserModel(id: "2", fullName: "Jane", email: "<EMAIL>")
        let user3 = UserModel(id: "3", fullName: "", email: "<EMAIL>")

        // When & Then
        #expect(user1.initials == "JD")
        #expect(user2.initials == "J")
        #expect(user3.initials == "")
    }

    @Test("UserModel display name")
    func testUserModelDisplayName() async throws {
        // Given
        let user1 = UserModel(id: "1", fullName: "John Doe", email: "<EMAIL>")
        let user2 = UserModel(id: "2", fullName: "", email: "<EMAIL>")

        // When & Then
        #expect(user1.displayName == "John Doe")
        #expect(user2.displayName == "Unknown User")
    }
    
    @Test("UserModel equality comparison")
    func testUserModelEquality() async throws {
        // Given
        let user1 = UserModel(id: "1", fullName: "John", email: "<EMAIL>")
        let user2 = UserModel(id: "1", fullName: "John", email: "<EMAIL>")
        let user3 = UserModel(id: "2", fullName: "Jane", email: "<EMAIL>")

        // Then
        #expect(user1 == user2)
        #expect(user1 != user3)
    }

    @Test("UserModel sample data")
    func testUserModelSampleData() async throws {
        // Given & When
        let sampleUser = UserModel.sample

        // Then
        #expect(sampleUser.id == "sample-user-id")
        #expect(sampleUser.fullName == "John Doe")
        #expect(sampleUser.email == "<EMAIL>")
        #expect(sampleUser.phoneNumber == "+1234567890")
        #expect(sampleUser.bio == "iOS Developer passionate about SwiftUI")
        #expect(sampleUser.profileImageUrl == nil)
        #expect(sampleUser.emailVerified == true)
    }

    @Test("UserModel Codable conformance")
    func testUserModelCodable() async throws {
        // Given
        let user = UserModel(
            id: "test-id",
            fullName: "Test User",
            email: "<EMAIL>",
            phoneNumber: "+1234567890",
            bio: "Test bio",
            profileImageUrl: "https://example.com/image.jpg",
            emailVerified: false
        )

        // When
        let encoder = JSONEncoder()
        let data = try encoder.encode(user)

        let decoder = JSONDecoder()
        let decodedUser = try decoder.decode(UserModel.self, from: data)

        // Then
        #expect(decodedUser.id == user.id)
        #expect(decodedUser.fullName == user.fullName)
        #expect(decodedUser.email == user.email)
        #expect(decodedUser.phoneNumber == user.phoneNumber)
        #expect(decodedUser.bio == user.bio)
        #expect(decodedUser.profileImageUrl == user.profileImageUrl)
        #expect(decodedUser.emailVerified == user.emailVerified)
    }
}
