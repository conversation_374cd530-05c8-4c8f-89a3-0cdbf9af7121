//
//  ChatMessageTests.swift
//  ZadrzTests
//
//  Created by <PERSON><PERSON><PERSON> on 6/22/25.
//

import Testing
@testable import Zadrz

struct ChatMessageTests {

    @Test("ChatMessage initialization with user message")
    func testUserMessageInitialization() async throws {
        // Given
        let text = "Hello, how are you?"
        let isFromUser = true
        let timestamp = "22:30"

        // When
        let message = ChatMessage(text: text, isFromUser: isFromUser, timestamp: timestamp)

        // Then
        #expect(message.text == text)
        #expect(message.isFromUser == isFromUser)
        #expect(message.timestamp == timestamp)
        #expect(message.messageType == .text)
        #expect(message.id != nil)
    }

    @Test("ChatMessage initialization with AI message")
    func testAIMessageInitialization() async throws {
        // Given
        let text = "I'm doing well, thank you!"
        let isFromUser = false
        let timestamp = "22:31"

        // When
        let message = ChatMessage(text: text, isFromUser: isFromUser, timestamp: timestamp)

        // Then
        #expect(message.text == text)
        #expect(message.isFromUser == isFromUser)
        #expect(message.timestamp == timestamp)
        #expect(message.messageType == .text)
        #expect(message.id != nil)
    }

    @Test("ChatMessage initialization with file message")
    func testFileMessageInitialization() async throws {
        // Given
        let text = "document.pdf"
        let isFromUser = true
        let timestamp = "22:32"
        let messageType = MessageType.file

        // When
        let message = ChatMessage(text: text, isFromUser: isFromUser, timestamp: timestamp, messageType: messageType)

        // Then
        #expect(message.text == text)
        #expect(message.isFromUser == isFromUser)
        #expect(message.timestamp == timestamp)
        #expect(message.messageType == .file)
        #expect(message.id != nil)
    }

    @Test("ChatMessage unique ID generation")
    func testUniqueIDGeneration() async throws {
        // Given & When
        let message1 = ChatMessage(text: "Test 1", isFromUser: true, timestamp: "22:30")
        let message2 = ChatMessage(text: "Test 2", isFromUser: true, timestamp: "22:31")

        // Then
        #expect(message1.id != message2.id)
    }

    @Test("ChatMessage image type")
    func testImageMessageType() async throws {
        // Given
        let text = "image.jpg"
        let isFromUser = false
        let timestamp = "22:33"
        let messageType = MessageType.image

        // When
        let message = ChatMessage(text: text, isFromUser: isFromUser, timestamp: timestamp, messageType: messageType)

        // Then
        #expect(message.messageType == .image)
    }

    @Test("ChatMessage date type")
    func testDateMessageType() async throws {
        // Given
        let text = "July 5"
        let isFromUser = false
        let timestamp = ""
        let messageType = MessageType.date

        // When
        let message = ChatMessage(text: text, isFromUser: isFromUser, timestamp: timestamp, messageType: messageType)

        // Then
        #expect(message.messageType == .date)
        #expect(message.timestamp.isEmpty)
    }

    @Test("MessageType enum cases")
    func testMessageTypeEnumCases() async throws {
        // Given & When & Then
        let textType = MessageType.text
        let fileType = MessageType.file
        let imageType = MessageType.image
        let dateType = MessageType.date

        #expect(textType != fileType)
        #expect(fileType != imageType)
        #expect(imageType != dateType)
        #expect(dateType != textType)
    }

    @Test("ChatMessage sample data")
    func testChatMessageSampleData() async throws {
        // Given & When
        let sampleMessages = ChatMessage.sampleMessages

        // Then
        #expect(!sampleMessages.isEmpty)
        #expect(sampleMessages.count == 7)

        // Test first message
        let firstMessage = sampleMessages[0]
        #expect(firstMessage.text == "dolor sit")
        #expect(firstMessage.isFromUser == true)
        #expect(firstMessage.timestamp == "22:30")
        #expect(firstMessage.messageType == .text)

        // Test file message
        let fileMessage = sampleMessages[3]
        #expect(fileMessage.text == "document_name.doc")
        #expect(fileMessage.messageType == .file)

        // Test date message
        let dateMessage = sampleMessages[4]
        #expect(dateMessage.text == "July 5")
        #expect(dateMessage.messageType == .date)
        #expect(dateMessage.timestamp.isEmpty)
    }
}
