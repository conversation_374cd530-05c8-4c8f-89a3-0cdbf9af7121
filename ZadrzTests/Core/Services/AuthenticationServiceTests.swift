//
//  AuthenticationServiceTests.swift
//  ZadrzTests
//
//  Created by <PERSON><PERSON><PERSON> on 6/22/25.
//

import Testing
import Foundation
@testable import Zadrz

struct AuthenticationServiceTests {

    @Test("AuthenticationService singleton instance")
    func testSingletonInstance() async throws {
        // Given & When
        let instance1 = AuthenticationService.shared
        let instance2 = AuthenticationService.shared

        // Then
        #expect(instance1 === instance2)
    }

    @Test("Authentication state initialization")
    func testAuthenticationStateInitialization() async throws {
        // Given
        let service = AuthenticationService.shared

        // When & Then
        // The auth state should be either pending or unauthenticated depending on Firebase state
        let currentState = service.authState.value
        #expect(currentState == .pending || currentState == .unauthenticated)
    }

    @Test("Current user property")
    func testCurrentUserProperty() async throws {
        // Given
        let service = AuthenticationService.shared

        // When & Then
        // Note: In a real test environment, this would be nil unless a user is logged in
        // We're just testing that the property is accessible
        let currentUser = service.currentUser
        #expect(currentUser == nil) // Should be nil in test environment
    }

    @Test("Authentication error descriptions")
    func testAuthenticationErrorDescriptions() async throws {
        // Given & When & Then
        #expect(AuthenticationError.userNotLoggedIn.errorDescription == "You need to be logged in to perform this action.")
        #expect(AuthenticationError.invalidEmail.errorDescription == "Please provide a valid email address.")
        #expect(AuthenticationError.invalidCredentials.errorDescription == "The email or password is incorrect.")
        #expect(AuthenticationError.networkError.errorDescription == "Network error. Please check your connection.")
        #expect(AuthenticationError.weakPassword.errorDescription == "Password should be at least 6 characters long.")
        #expect(AuthenticationError.emailVerificationRequired.errorDescription == "A verification email has been sent to your new email address. Please verify it to complete the email change.")
        #expect(AuthenticationError.unknownError.errorDescription == "An unknown error occurred.")
    }

    @Test("Authentication state enum")
    func testAuthenticationStateEnum() async throws {
        // Given & When & Then
        let pending = AuthenticationState.pending
        let authenticated = AuthenticationState.authenticated
        let unauthenticated = AuthenticationState.unauthenticated

        #expect(pending != authenticated)
        #expect(authenticated != unauthenticated)
        #expect(pending != unauthenticated)
    }

    @Test("Service protocol conformance")
    func testServiceProtocolConformance() async throws {
        // Given
        let service = AuthenticationService.shared

        // When & Then
        // Test that the service conforms to the protocol
        #expect(service is AuthenticationServiceProtocol)

        // Test that required properties are accessible
        #expect(service.authState != nil)

        // Test that the current user property is accessible (will be nil in test)
        let currentUser = service.currentUser
        #expect(currentUser == nil)
    }

    @Test("Auto login functionality")
    func testAutoLogin() async throws {
        // Given
        let service = AuthenticationService.shared

        // When
        await service.autoLogin()

        // Then
        // In test environment, should be unauthenticated
        #expect(service.authState.value == .unauthenticated)
    }
}
