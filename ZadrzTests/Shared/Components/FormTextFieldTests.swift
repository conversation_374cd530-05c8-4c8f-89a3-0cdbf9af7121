//
//  FormTextFieldTests.swift
//  ZadrzTests
//
//  Created by <PERSON><PERSON><PERSON> on 6/22/25.
//

import Testing
import Swift<PERSON>
@testable import Zadrz

struct FormTextFieldTests {
    
    @Test("FormTextField initialization with basic parameters")
    func testBasicInitialization() async throws {
        // Given
        let title = "Email"
        let placeholder = "Enter your email"
        let text = Binding.constant("")
        
        // When
        let formTextField = FormTextField(
            title: title,
            placeholder: placeholder,
            text: text
        )
        
        // Then
        // Note: In SwiftUI testing, we typically test the view model or data logic
        // rather than the view itself. Here we're testing that the component
        // can be initialized without errors.
        #expect(formTextField != nil)
    }
    
    @Test("FormTextField initialization with secure text")
    func testSecureTextInitialization() async throws {
        // Given
        let title = "Password"
        let placeholder = "Enter your password"
        let text = Binding.constant("")
        let isSecure = true
        
        // When
        let formTextField = FormTextField(
            title: title,
            placeholder: placeholder,
            text: text,
            isSecure: isSecure
        )
        
        // Then
        #expect(formTextField != nil)
    }
    
    @Test("FormTextField initialization with error message")
    func testErrorMessageInitialization() async throws {
        // Given
        let title = "Email"
        let placeholder = "Enter your email"
        let text = Binding.constant("<EMAIL>")
        let errorMessage = "Invalid email format"

        // When
        let formTextField = FormTextField(
            title: title,
            placeholder: placeholder,
            text: text,
            errorMessage: errorMessage
        )

        // Then
        #expect(formTextField != nil)
    }

    @Test("FormTextField initialization with all parameters")
    func testFullInitialization() async throws {
        // Given
        let title = "Password"
        let placeholder = "Enter your password"
        let text = Binding.constant("password123")
        let isSecure = true
        let keyboardType = UIKeyboardType.default
        let autocapitalization = TextInputAutocapitalization.never
        let errorMessage = "Password must be at least 8 characters"

        // When
        let formTextField = FormTextField(
            title: title,
            placeholder: placeholder,
            text: text,
            isSecure: isSecure,
            keyboardType: keyboardType,
            autocapitalization: autocapitalization,
            errorMessage: errorMessage
        )

        // Then
        #expect(formTextField != nil)
    }
    
    @Test("FormTextField text binding")
    func testTextBinding() async throws {
        // Given
        var textValue = "initial"
        let text = Binding(
            get: { textValue },
            set: { textValue = $0 }
        )
        
        // When
        let formTextField = FormTextField(
            title: "Test",
            placeholder: "Test placeholder",
            text: text
        )
        
        // Then
        #expect(formTextField != nil)
        #expect(textValue == "initial")
        
        // When text is updated
        textValue = "updated"
        #expect(textValue == "updated")
    }
    
    @Test("FormTextField error states")
    func testErrorStates() async throws {
        // Given
        let text = Binding.constant("<EMAIL>")

        // When - No error state
        let validTextField = FormTextField(
            title: "Email",
            placeholder: "Enter email",
            text: text,
            errorMessage: nil
        )

        // Then
        #expect(validTextField != nil)

        // When - Error state
        let invalidTextField = FormTextField(
            title: "Email",
            placeholder: "Enter email",
            text: text,
            errorMessage: "Invalid email"
        )

        // Then
        #expect(invalidTextField != nil)
    }
    
    @Test("FormTextField keyboard types")
    func testKeyboardTypes() async throws {
        // Given
        let text = Binding.constant("")
        
        // When - Email keyboard
        let emailField = FormTextField(
            title: "Email",
            placeholder: "Enter email",
            text: text,
            keyboardType: .emailAddress
        )
        
        // Then
        #expect(emailField != nil)
        
        // When - Number keyboard
        let numberField = FormTextField(
            title: "Phone",
            placeholder: "Enter phone",
            text: text,
            keyboardType: .phonePad
        )
        
        // Then
        #expect(numberField != nil)
    }
    
    @Test("FormTextField autocapitalization")
    func testAutocapitalization() async throws {
        // Given
        let text = Binding.constant("")

        // When - Email field with no autocapitalization
        let emailField = FormTextField(
            title: "Email",
            placeholder: "Enter email",
            text: text,
            keyboardType: .emailAddress,
            autocapitalization: .never
        )

        // Then
        #expect(emailField != nil)

        // When - Name field with words autocapitalization
        let nameField = FormTextField(
            title: "Name",
            placeholder: "Enter name",
            text: text,
            autocapitalization: .words
        )

        // Then
        #expect(nameField != nil)
    }
    
    @Test("FormTextField accessibility")
    func testAccessibility() async throws {
        // Given
        let title = "Email Address"
        let placeholder = "Enter your email address"
        let text = Binding.constant("")
        
        // When
        let formTextField = FormTextField(
            title: title,
            placeholder: placeholder,
            text: text
        )
        
        // Then
        // Test that the component can be created with accessibility in mind
        #expect(formTextField != nil)
    }
}
