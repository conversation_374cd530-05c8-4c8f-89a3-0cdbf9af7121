//
//  AuthenticationViewModelTests.swift
//  ZadrzTests
//
//  Created by <PERSON><PERSON><PERSON> on 6/22/25.
//

import Testing
@testable import Zadrz

@MainActor
struct AuthenticationViewModelTests {
    
    @Test("AuthenticationViewModel initialization")
    func testInitialization() async throws {
        // Given & When
        let viewModel = AuthenticationViewModel()

        // Then
        #expect(viewModel.email.isEmpty)
        #expect(viewModel.password.isEmpty)
        #expect(viewModel.fullName.isEmpty)
        #expect(viewModel.phoneNumber.isEmpty)
        #expect(viewModel.currentPassword.isEmpty)
        #expect(viewModel.newEmail.isEmpty)
        #expect(viewModel.newPassword.isEmpty)
        #expect(viewModel.passwordConfirmation.isEmpty)

        #expect(!viewModel.isAuthenticated)
        #expect(!viewModel.isLoading)
        #expect(!viewModel.isGoogleSignInLoading)
        #expect(!viewModel.isAppleSignInLoading)
        #expect(!viewModel.isEmailSignInLoading)
        #expect(!viewModel.isSignUpLoading)

        #expect(!viewModel.successTrigger)
        #expect(!viewModel.errorTrigger)
        #expect(!viewModel.didLogout)
        #expect(!viewModel.showSuccessAlert)
        #expect(!viewModel.showErrorAlert)

        #expect(viewModel.successMessage == nil)
        #expect(viewModel.errorMessage == nil)
        #expect(viewModel.currentUser == nil)
    }
    
    @Test("Email validation using String extension")
    func testEmailValidation() async throws {
        // Given
        let viewModel = AuthenticationViewModel()

        // When & Then
        viewModel.email = "<EMAIL>"
        #expect(viewModel.email.isValidEmail)

        viewModel.email = "invalid-email"
        #expect(!viewModel.email.isValidEmail)

        viewModel.email = ""
        #expect(!viewModel.email.isValidEmail)
    }

    @Test("Password validation using String extension")
    func testPasswordValidation() async throws {
        // Given
        let viewModel = AuthenticationViewModel()

        // When & Then
        viewModel.password = "ValidPass123!"
        #expect(viewModel.password.isValidPassword)

        viewModel.password = "weak"
        #expect(!viewModel.password.isValidPassword)

        viewModel.password = ""
        #expect(!viewModel.password.isValidPassword)
    }

    @Test("Full name validation")
    func testFullNameValidation() async throws {
        // Given
        let viewModel = AuthenticationViewModel()

        // When & Then
        viewModel.fullName = "John Doe"
        #expect(!viewModel.fullName.isEmpty)

        viewModel.fullName = "A"
        #expect(!viewModel.fullName.isEmpty) // Single character is still not empty

        viewModel.fullName = ""
        #expect(viewModel.fullName.isEmpty)
    }
    
    @Test("Form field properties")
    func testFormFieldProperties() async throws {
        // Given
        let viewModel = AuthenticationViewModel()

        // When
        viewModel.email = "<EMAIL>"
        viewModel.fullName = "John Doe"
        viewModel.password = "password123"
        viewModel.phoneNumber = "+1234567890"
        viewModel.currentPassword = "oldpass"
        viewModel.newEmail = "<EMAIL>"
        viewModel.newPassword = "newpass123"
        viewModel.passwordConfirmation = "newpass123"

        // Then
        #expect(viewModel.email == "<EMAIL>")
        #expect(viewModel.fullName == "John Doe")
        #expect(viewModel.password == "password123")
        #expect(viewModel.phoneNumber == "+1234567890")
        #expect(viewModel.currentPassword == "oldpass")
        #expect(viewModel.newEmail == "<EMAIL>")
        #expect(viewModel.newPassword == "newpass123")
        #expect(viewModel.passwordConfirmation == "newpass123")
    }

    @Test("Loading states")
    func testLoadingStates() async throws {
        // Given
        let viewModel = AuthenticationViewModel()

        // When & Then
        #expect(!viewModel.isGoogleSignInLoading)
        #expect(!viewModel.isAppleSignInLoading)
        #expect(!viewModel.isEmailSignInLoading)
        #expect(!viewModel.isSignUpLoading)

        viewModel.isGoogleSignInLoading = true
        #expect(viewModel.isGoogleSignInLoading)

        viewModel.isAppleSignInLoading = true
        #expect(viewModel.isAppleSignInLoading)

        viewModel.isEmailSignInLoading = true
        #expect(viewModel.isEmailSignInLoading)

        viewModel.isSignUpLoading = true
        #expect(viewModel.isSignUpLoading)
    }

    @Test("Success message handling")
    func testSuccessMessageHandling() async throws {
        // Given
        let viewModel = AuthenticationViewModel()
        let message = "Success!"

        // When
        viewModel.showSuccess(message)

        // Then
        #expect(viewModel.successMessage == message)
        #expect(viewModel.showSuccessAlert)
        #expect(viewModel.successTrigger)
    }

    @Test("Error message handling")
    func testErrorMessageHandling() async throws {
        // Given
        let viewModel = AuthenticationViewModel()
        let message = "Error occurred!"

        // When
        viewModel.showError(message)

        // Then
        #expect(viewModel.errorMessage == message)
        #expect(viewModel.showErrorAlert)
        #expect(viewModel.errorTrigger)
    }

    @Test("Authentication state properties")
    func testAuthenticationStateProperties() async throws {
        // Given
        let viewModel = AuthenticationViewModel()

        // When & Then
        #expect(!viewModel.isAuthenticated)
        #expect(viewModel.currentUser == nil)
        #expect(!viewModel.didLogout)

        // Test that properties can be set
        viewModel.isAuthenticated = true
        #expect(viewModel.isAuthenticated)

        viewModel.didLogout = true
        #expect(viewModel.didLogout)
    }

    @Test("Feedback triggers")
    func testFeedbackTriggers() async throws {
        // Given
        let viewModel = AuthenticationViewModel()

        // When & Then
        #expect(!viewModel.successTrigger)
        #expect(!viewModel.errorTrigger)

        viewModel.successTrigger = true
        #expect(viewModel.successTrigger)

        viewModel.errorTrigger = true
        #expect(viewModel.errorTrigger)
    }
}
