//
//  ChatViewModelTests.swift
//  ZadrzTests
//
//  Created by <PERSON><PERSON><PERSON> on 6/22/25.
//

import Testing
import Foundation
@testable import Zadrz

@MainActor
struct ChatsViewModelTests {

    @Test("ChatsViewModel initialization")
    func testInitialization() async throws {
        // Given & When
        let viewModel = ChatsViewModel()

        // Then
        #expect(!viewModel.chats.isEmpty)
        #expect(viewModel.selectedFilter == .active)
        #expect(!viewModel.isLoading)
    }

    @Test("Chat filtering functionality")
    func testChatFiltering() async throws {
        // Given
        let viewModel = ChatsViewModel()

        // When
        let activeChats = viewModel.filteredChats(for: .active)
        let favoriteChats = viewModel.filteredChats(for: .favorite)
        let savedChats = viewModel.filteredChats(for: .saved)

        // Then
        #expect(!activeChats.isEmpty)
        #expect(!favoriteChats.isEmpty) // Returns all chats for now
        #expect(!savedChats.isEmpty) // Returns all chats for now
    }

    @Test("Delete chat functionality")
    func testDeleteChat() async throws {
        // Given
        let viewModel = ChatsViewModel()
        let initialCount = viewModel.chats.count

        // When
        viewModel.deleteChat(at: IndexSet(integer: 0))

        // Then
        #expect(viewModel.chats.count == initialCount - 1)
    }

    @Test("Refresh chats functionality")
    func testRefreshChats() async throws {
        // Given
        let viewModel = ChatsViewModel()

        // When
        await viewModel.refreshChats()

        // Then
        #expect(!viewModel.isLoading)
    }

    @Test("Chat filter enum")
    func testChatFilterEnum() async throws {
        // Given & When & Then
        #expect(ChatFilter.active.title == "Active Chats")
        #expect(ChatFilter.favorite.title == "Favorite")
        #expect(ChatFilter.saved.title == "Saved Chats")

        #expect(ChatFilter.active.rawValue == "Active Chats")
        #expect(ChatFilter.favorite.rawValue == "Favorite")
        #expect(ChatFilter.saved.rawValue == "Saved Chats")
    }
}

@MainActor
struct ChatDetailViewModelTests {

    @Test("ChatDetailViewModel initialization")
    func testInitialization() async throws {
        // Given & When
        let viewModel = ChatDetailViewModel()

        // Then
        #expect(viewModel.messages.isEmpty)
        #expect(viewModel.messageText.isEmpty)
        #expect(!viewModel.isLoading)
        #expect(!viewModel.isTyping)
    }

    @Test("Load messages for chat")
    func testLoadMessages() async throws {
        // Given
        let viewModel = ChatDetailViewModel()
        let sampleChat = ChatItem.sampleChats.first!

        // When
        viewModel.loadMessages(for: sampleChat)

        // Then
        #expect(!viewModel.messages.isEmpty)
        #expect(viewModel.messages.count == ChatMessage.sampleMessages.count)
    }

    @Test("Send message functionality")
    func testSendMessage() async throws {
        // Given
        let viewModel = ChatDetailViewModel()
        viewModel.messageText = "Hello, world!"
        let initialCount = viewModel.messages.count

        // When
        viewModel.sendMessage()

        // Then
        #expect(viewModel.messages.count == initialCount + 1)
        #expect(viewModel.messageText.isEmpty)
        #expect(viewModel.messages.last?.text == "Hello, world!")
        #expect(viewModel.messages.last?.isFromUser == true)
    }

    @Test("Send empty message validation")
    func testSendEmptyMessage() async throws {
        // Given
        let viewModel = ChatDetailViewModel()
        viewModel.messageText = ""
        let initialCount = viewModel.messages.count

        // When
        viewModel.sendMessage()

        // Then
        #expect(viewModel.messages.count == initialCount)
    }

    @Test("Send whitespace message validation")
    func testSendWhitespaceMessage() async throws {
        // Given
        let viewModel = ChatDetailViewModel()
        viewModel.messageText = "   "
        let initialCount = viewModel.messages.count

        // When
        viewModel.sendMessage()

        // Then
        #expect(viewModel.messages.count == initialCount)
    }

    @Test("Attach file functionality")
    func testAttachFile() async throws {
        // Given
        let viewModel = ChatDetailViewModel()

        // When & Then
        // This should not crash - it's a placeholder implementation
        viewModel.attachFile()
        #expect(Bool(true)) // Test passes if no crash occurs
    }

    @Test("Start voice message functionality")
    func testStartVoiceMessage() async throws {
        // Given
        let viewModel = ChatDetailViewModel()

        // When & Then
        // This should not crash - it's a placeholder implementation
        viewModel.startVoiceMessage()
        #expect(Bool(true)) // Test passes if no crash occurs
    }

    @Test("Message text property")
    func testMessageTextProperty() async throws {
        // Given
        let viewModel = ChatDetailViewModel()

        // When
        viewModel.messageText = "Test message"

        // Then
        #expect(viewModel.messageText == "Test message")

        // When
        viewModel.messageText = ""

        // Then
        #expect(viewModel.messageText.isEmpty)
    }

    @Test("Loading and typing states")
    func testLoadingAndTypingStates() async throws {
        // Given
        let viewModel = ChatDetailViewModel()

        // When & Then
        #expect(!viewModel.isLoading)
        #expect(!viewModel.isTyping)

        viewModel.isLoading = true
        #expect(viewModel.isLoading)

        viewModel.isTyping = true
        #expect(viewModel.isTyping)
    }
}
